# Km_Data.xlsx

- 因smiles中有逗号，所以不能使用csv存储数据

- 全部的Km数据集以0.8进行聚类后的结果
- Value列是原始的Km数值，单位是mM
- Log10_Km_Value是Km数值进行log10变换的结果（大多使用这一列）
- M_Log10_Km_Value是Km数值的单位转换为M后再进行log10变换的结果

# C_data.xlsx

- 中心碳代谢数据集，由于在挑选这部分数据集之后又对总数据集进行了优化，所以可能存在一小部分不存在于Km_Data总数据集中的数据
- Log10_Km_Value是Km数值进行log10变换的结果，单位是mM

# KM_data.zip

- 获取Km数据集的原始代码和数据

# codon.xlsx

- 密码子对照表（ncbi）

# BL21data.xlsx

- 整理后的BL21数据集

# BL21.zip

- ncbi的原始数据
