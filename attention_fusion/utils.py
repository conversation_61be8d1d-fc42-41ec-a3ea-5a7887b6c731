"""
注意力融合模型的工具函数
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


def visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,
                              title: str = "Attention Weights", save_path: Optional[str] = None,
                              figsize: Tuple[int, int] = (10, 8)):
    """
    可视化注意力权重矩阵
    
    Args:
        attention_weights: 注意力权重 (seq_len, seq_len)
        sequence: 蛋白质序列
        title: 图像标题
        save_path: 保存路径
        figsize: 图像大小
    """
    attention_weights = attention_weights.cpu().numpy() if isinstance(attention_weights, torch.Tensor) else attention_weights
    
    plt.figure(figsize=figsize)
    
    # 创建热图
    sns.heatmap(attention_weights, 
                cmap='Blues', 
                cbar=True,
                square=True,
                linewidths=0.1,
                cbar_kws={'label': 'Attention Weight'})
    
    plt.title(title)
    plt.xlabel('Key Position')
    plt.ylabel('Query Position')
    
    # 如果序列较短，添加氨基酸标签
    if len(sequence) <= 50:
        plt.xticks(range(len(sequence)), list(sequence), rotation=90)
        plt.yticks(range(len(sequence)), list(sequence), rotation=0)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Attention weights visualization saved to {save_path}")
    
    plt.show()


def visualize_cross_attention_flow(esm_to_msa_weights: torch.Tensor, 
                                  msa_to_esm_weights: torch.Tensor,
                                  sequence: str, save_path: Optional[str] = None):
    """
    可视化交叉注意力流
    
    Args:
        esm_to_msa_weights: ESM-2到MSA的注意力权重
        msa_to_esm_weights: MSA到ESM-2的注意力权重
        sequence: 蛋白质序列
        save_path: 保存路径
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # ESM-2 -> MSA 注意力
    esm_to_msa = esm_to_msa_weights.cpu().numpy() if isinstance(esm_to_msa_weights, torch.Tensor) else esm_to_msa_weights
    sns.heatmap(esm_to_msa, ax=ax1, cmap='Reds', cbar=True, square=True)
    ax1.set_title('ESM-2 → MSA Attention')
    ax1.set_xlabel('MSA Position')
    ax1.set_ylabel('ESM-2 Position')
    
    # MSA -> ESM-2 注意力
    msa_to_esm = msa_to_esm_weights.cpu().numpy() if isinstance(msa_to_esm_weights, torch.Tensor) else msa_to_esm_weights
    sns.heatmap(msa_to_esm, ax=ax2, cmap='Blues', cbar=True, square=True)
    ax2.set_title('MSA → ESM-2 Attention')
    ax2.set_xlabel('ESM-2 Position')
    ax2.set_ylabel('MSA Position')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Cross attention flow visualization saved to {save_path}")
    
    plt.show()


def analyze_attention_patterns(attention_weights: List[Dict], sequence: str) -> Dict[str, float]:
    """
    分析注意力模式
    
    Args:
        attention_weights: 注意力权重列表
        sequence: 蛋白质序列
        
    Returns:
        注意力模式分析结果
    """
    # 计算平均注意力权重
    avg_esm_to_msa = torch.stack([w["esm_to_msa_weights"] for w in attention_weights]).mean(dim=0)
    avg_msa_to_esm = torch.stack([w["msa_to_esm_weights"] for w in attention_weights]).mean(dim=0)
    
    if isinstance(avg_esm_to_msa, torch.Tensor):
        avg_esm_to_msa = avg_esm_to_msa.cpu().numpy()
    if isinstance(avg_msa_to_esm, torch.Tensor):
        avg_msa_to_esm = avg_msa_to_esm.cpu().numpy()
    
    analysis = {
        # 注意力集中度
        "esm_to_msa_concentration": float(np.max(avg_esm_to_msa, axis=1).mean()),
        "msa_to_esm_concentration": float(np.max(avg_msa_to_esm, axis=1).mean()),
        
        # 注意力分散度
        "esm_to_msa_entropy": float(-np.sum(avg_esm_to_msa * np.log(avg_esm_to_msa + 1e-8), axis=1).mean()),
        "msa_to_esm_entropy": float(-np.sum(avg_msa_to_esm * np.log(avg_msa_to_esm + 1e-8), axis=1).mean()),
        
        # 对角线注意力（自注意力程度）
        "esm_to_msa_diagonal": float(np.diag(avg_esm_to_msa).mean()),
        "msa_to_esm_diagonal": float(np.diag(avg_msa_to_esm).mean()),
        
        # 注意力范围
        "esm_to_msa_range": float(np.std(np.argmax(avg_esm_to_msa, axis=1))),
        "msa_to_esm_range": float(np.std(np.argmax(avg_msa_to_esm, axis=1))),
    }
    
    return analysis


def compute_feature_similarity(features1: torch.Tensor, features2: torch.Tensor, 
                             method: str = "cosine") -> Dict[str, float]:
    """
    计算特征相似性
    
    Args:
        features1: 第一组特征
        features2: 第二组特征
        method: 相似性计算方法
        
    Returns:
        相似性分析结果
    """
    if method == "cosine":
        similarity = torch.nn.functional.cosine_similarity(features1, features2, dim=-1)
        return {
            "mean_similarity": float(similarity.mean()),
            "std_similarity": float(similarity.std()),
            "min_similarity": float(similarity.min()),
            "max_similarity": float(similarity.max())
        }
    elif method == "l2":
        distance = torch.norm(features1 - features2, dim=-1)
        return {
            "mean_distance": float(distance.mean()),
            "std_distance": float(distance.std()),
            "min_distance": float(distance.min()),
            "max_distance": float(distance.max())
        }
    else:
        raise ValueError(f"Unknown similarity method: {method}")


def plot_feature_evolution(features_by_layer: List[torch.Tensor], sequence: str,
                          save_path: Optional[str] = None):
    """
    绘制特征在不同层的演化
    
    Args:
        features_by_layer: 不同层的特征列表
        sequence: 蛋白质序列
        save_path: 保存路径
    """
    num_layers = len(features_by_layer)
    fig, axes = plt.subplots(1, num_layers, figsize=(4 * num_layers, 6))
    
    if num_layers == 1:
        axes = [axes]
    
    for i, features in enumerate(features_by_layer):
        if isinstance(features, torch.Tensor):
            features = features.cpu().numpy()
        
        # 取前几个维度进行可视化
        features_vis = features[:, :min(50, features.shape[1])]
        
        im = axes[i].imshow(features_vis.T, cmap='viridis', aspect='auto')
        axes[i].set_title(f'Layer {i+1}')
        axes[i].set_xlabel('Residue Position')
        axes[i].set_ylabel('Feature Dimension')
        
        plt.colorbar(im, ax=axes[i])
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Feature evolution plot saved to {save_path}")
    
    plt.show()


def save_attention_analysis(results: Dict[str, torch.Tensor], save_path: str):
    """
    保存注意力分析结果
    
    Args:
        results: 分析结果
        save_path: 保存路径
    """
    # 转换为numpy格式保存
    results_np = {}
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            results_np[key] = value.cpu().numpy()
        elif isinstance(value, list):
            # 处理注意力权重列表
            if key == "attention_weights":
                attention_data = {}
                for i, attention_dict in enumerate(value):
                    for att_key, att_value in attention_dict.items():
                        if isinstance(att_value, torch.Tensor):
                            attention_data[f"layer_{i}_{att_key}"] = att_value.cpu().numpy()
                results_np[key] = attention_data
            else:
                results_np[key] = value
        else:
            results_np[key] = value
    
    np.savez_compressed(save_path, **results_np)
    logger.info(f"Attention analysis results saved to {save_path}")


def load_attention_analysis(load_path: str) -> Dict:
    """
    加载注意力分析结果
    
    Args:
        load_path: 文件路径
        
    Returns:
        分析结果字典
    """
    data = np.load(load_path, allow_pickle=True)
    results = {}
    
    for key in data.files:
        value = data[key]
        if isinstance(value, np.ndarray) and value.dtype != object:
            results[key] = torch.from_numpy(value)
        else:
            results[key] = value.item() if value.ndim == 0 else value
    
    logger.info(f"Attention analysis results loaded from {load_path}")
    return results


def print_attention_summary(results: Dict[str, torch.Tensor]):
    """
    打印注意力分析摘要
    
    Args:
        results: 模型输出结果
    """
    print("=" * 60)
    print("ATTENTION FUSION ANALYSIS SUMMARY")
    print("=" * 60)
    
    if "attention_weights" in results:
        attention_weights = results["attention_weights"]
        print(f"Number of attention layers: {len(attention_weights)}")
        
        # 分析注意力模式
        if attention_weights:
            first_layer = attention_weights[0]
            if "esm_to_msa_weights" in first_layer:
                esm_to_msa = first_layer["esm_to_msa_weights"]
                print(f"ESM-2 to MSA attention shape: {esm_to_msa.shape}")
                print(f"Average ESM-2 to MSA attention: {esm_to_msa.mean().item():.4f}")
            
            if "msa_to_esm_weights" in first_layer:
                msa_to_esm = first_layer["msa_to_esm_weights"]
                print(f"MSA to ESM-2 attention shape: {msa_to_esm.shape}")
                print(f"Average MSA to ESM-2 attention: {msa_to_esm.mean().item():.4f}")
    
    if "final_esm_contribution" in results:
        esm_contrib = results["final_esm_contribution"].mean().item()
        print(f"Final ESM-2 contribution: {esm_contrib:.4f}")
    
    if "final_msa_contribution" in results:
        msa_contrib = results["final_msa_contribution"].mean().item()
        print(f"Final MSA contribution: {msa_contrib:.4f}")
    
    print("=" * 60)


def validate_attention_fusion_input(esm_features: torch.Tensor, msa_features: Dict) -> bool:
    """
    验证注意力融合模型的输入
    
    Args:
        esm_features: ESM-2特征
        msa_features: MSA特征字典
        
    Returns:
        是否为有效输入
    """
    try:
        # 检查ESM-2特征
        if not isinstance(esm_features, torch.Tensor):
            logger.error("ESM-2 features must be a torch.Tensor")
            return False
        
        if len(esm_features.shape) != 3:
            logger.error("ESM-2 features must have shape (batch_size, seq_len, feature_dim)")
            return False
        
        # 检查MSA特征
        if not isinstance(msa_features, dict):
            logger.error("MSA features must be a dictionary")
            return False
        
        # 检查必要的MSA特征
        required_keys = ["query_representation", "pssm", "conservation_scores"]
        if not any(key in msa_features for key in required_keys):
            logger.error(f"MSA features must contain at least one of: {required_keys}")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"Input validation failed: {e}")
        return False
