#!/usr/bin/env python3
"""
修复版本的特征提取器 - 解决MSA Transformer和HHblits问题
"""

import torch
import numpy as np
import logging
import tempfile
import subprocess
import os
from typing import Dict, List, Optional, Union
from Bio import SeqIO
import esm
from transformers import EsmTokenizer, EsmModel

logger = logging.getLogger(__name__)

class MSATransformerFeatureExtractor:
    """MSA Transformer特征提取器 - 修复版本"""
    
    def __init__(self, model_name: str = "facebook/esm_msa1b_t12_100M_UR50S"):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model_name = model_name
        
        # 尝试加载MSA模型
        try:
            logger.info(f"Loading MSA Transformer model: {model_name}")
            
            # 首先尝试使用fair-esm库
            try:
                logger.info("尝试使用fair-esm库加载MSA模型...")
                self.msa_model, self.msa_alphabet = esm.pretrained.esm_msa1b_t12_100M_UR50S()
                self.msa_model.to(self.device)
                self.msa_model.eval()
                self.msa_batch_converter = self.msa_alphabet.get_batch_converter()
                self.msa_dim = 768  # MSA模型的隐藏维度
                self.use_fair_esm_msa = True
                logger.info("✅ MSA Transformer loaded successfully with fair-esm")
                
            except Exception as e:
                logger.warning(f"Fair-esm MSA loading failed: {e}. Trying transformers...")
                # 回退到transformers库
                from transformers import EsmTokenizer, EsmModel
                self.msa_tokenizer = EsmTokenizer.from_pretrained(model_name)
                self.msa_model = EsmModel.from_pretrained(model_name)
                self.msa_model.to(self.device)
                self.msa_model.eval()
                self.msa_dim = self.msa_model.config.hidden_size
                self.use_fair_esm_msa = False
                logger.info("✅ MSA Transformer loaded successfully with transformers")

        except Exception as e:
            logger.warning(f"Failed to load MSA Transformer: {e}. Using traditional MSA features.")
            self.msa_model = None
            self.msa_tokenizer = None
            self.msa_dim = 256  # 传统MSA特征维度
            self.use_fair_esm_msa = False
    
    def _preprocess_msa_sequences(self, msa_sequences: List[str]) -> List[str]:
        """预处理MSA序列，确保数据类型正确"""
        processed_sequences = []
        
        for seq in msa_sequences:
            # 确保序列是字符串类型
            if isinstance(seq, list):
                # 如果是列表，取第一个元素或连接
                if len(seq) > 0:
                    seq = str(seq[0]) if not isinstance(seq[0], str) else seq[0]
                else:
                    continue
            elif not isinstance(seq, str):
                seq = str(seq)
            
            # 清理序列
            seq = seq.strip().upper()
            # 移除非标准氨基酸字符
            seq = ''.join(c for c in seq if c in 'ACDEFGHIKLMNPQRSTVWY')
            
            if len(seq) > 0:
                processed_sequences.append(seq)
        
        # 确保至少有一个序列
        if not processed_sequences and msa_sequences:
            # 如果处理后没有序列，使用原始第一个序列
            fallback_seq = str(msa_sequences[0]).strip().upper()
            fallback_seq = ''.join(c for c in fallback_seq if c.isalpha())
            if fallback_seq:
                processed_sequences = [fallback_seq]
        
        return processed_sequences
    
    def extract_msa_transformer_features(self, msa_sequences: List[str]) -> Dict[str, torch.Tensor]:
        """
        提取MSA Transformer特征 - 修复版本
        
        Args:
            msa_sequences: MSA序列列表
            
        Returns:
            MSA特征字典
        """
        # 预处理序列
        msa_sequences = self._preprocess_msa_sequences(msa_sequences)
        
        if not msa_sequences:
            logger.warning("No valid MSA sequences found, using single dummy sequence")
            msa_sequences = ["ACDEFGHIKLMNPQRSTVWY"]  # 使用标准氨基酸序列
        
        # 如果没有MSA模型，使用传统特征
        if self.msa_model is None:
            logger.info("Using traditional MSA features (no MSA model available)")
            return self._extract_traditional_msa_features(msa_sequences)
        
        try:
            if self.use_fair_esm_msa:
                # 使用fair-esm的MSA模型
                # 确保序列长度一致
                max_len = max(len(seq) for seq in msa_sequences)
                aligned_sequences = []
                
                for seq in msa_sequences:
                    if len(seq) < max_len:
                        # 用'-'填充到相同长度
                        seq = seq + '-' * (max_len - len(seq))
                    aligned_sequences.append(seq)
                
                # 限制MSA深度和长度
                if len(aligned_sequences) > 128:
                    aligned_sequences = aligned_sequences[:128]
                
                if max_len > 1024:
                    aligned_sequences = [seq[:1024] for seq in aligned_sequences]
                
                msa_data = [("msa", aligned_sequences)]
                msa_batch_labels, msa_batch_strs, msa_batch_tokens = self.msa_batch_converter(msa_data)
                msa_batch_tokens = msa_batch_tokens.to(self.device)

                with torch.no_grad():
                    results = self.msa_model(msa_batch_tokens, repr_layers=[12], return_contacts=True)

                # 提取特征
                msa_representations = results["representations"][12]  # (batch_size, num_seqs, seq_len, hidden_dim)

                features = {
                    "msa_representation": msa_representations,
                    "query_representation": msa_representations[0, 0, 1:-1, :],  # 第一个序列，去掉特殊token
                    "msa_attention": results.get("attentions", None),
                }

            else:
                # 使用transformers的MSA模型 - 修复tokenizer输入
                # 将序列列表转换为正确的格式
                aligned_sequences = msa_sequences[:64]  # 限制MSA深度
                
                # 确保序列长度一致
                if len(aligned_sequences) > 1:
                    max_len = max(len(seq) for seq in aligned_sequences)
                    aligned_sequences = [seq.ljust(max_len, '-') for seq in aligned_sequences]
                
                # 限制序列长度
                if aligned_sequences and len(aligned_sequences[0]) > 1024:
                    aligned_sequences = [seq[:1024] for seq in aligned_sequences]
                
                # 正确的tokenizer输入格式
                try:
                    # 方法1: 直接传入序列列表
                    inputs = self.msa_tokenizer(
                        aligned_sequences, 
                        return_tensors="pt", 
                        padding=True, 
                        truncation=True,
                        max_length=1024
                    )
                except Exception as e:
                    logger.warning(f"Tokenizer method 1 failed: {e}, trying method 2")
                    # 方法2: 使用batch格式
                    try:
                        inputs = self.msa_tokenizer(
                            [aligned_sequences], 
                            return_tensors="pt", 
                            padding=True,
                            is_split_into_words=False
                        )
                    except Exception as e2:
                        logger.warning(f"Tokenizer method 2 failed: {e2}, using fallback")
                        # 方法3: 只使用第一个序列
                        inputs = self.msa_tokenizer(
                            aligned_sequences[0], 
                            return_tensors="pt", 
                            padding=True, 
                            truncation=True
                        )
                
                inputs = {k: v.to(self.device) for k, v in inputs.items()}

                with torch.no_grad():
                    outputs = self.msa_model(**inputs, output_attentions=True)

                # 提取特征
                features = {
                    "msa_representation": outputs.last_hidden_state,
                    "msa_attention": torch.stack([layer_attention for layer_attention in outputs.attentions]),
                    "query_representation": outputs.last_hidden_state[0, 0, :, :] if outputs.last_hidden_state.dim() == 4 else outputs.last_hidden_state[0, :, :],
                }

            return features

        except Exception as e:
            logger.warning(f"MSA Transformer extraction failed: {e}. Using traditional features.")
            return self._extract_traditional_msa_features(msa_sequences)
    
    def _extract_traditional_msa_features(self, msa_sequences: List[str]) -> Dict[str, torch.Tensor]:
        """提取传统MSA特征作为备选"""
        if not msa_sequences:
            msa_sequences = ["ACDEFGHIKLMNPQRSTVWY"]
        
        query_seq = msa_sequences[0]
        seq_len = len(query_seq)
        
        # 计算PSSM
        pssm = self._calculate_pssm(msa_sequences, seq_len)
        
        # 计算保守性
        conservation = self._calculate_conservation(msa_sequences)
        
        # 计算共进化
        coevolution = self._calculate_coevolution(msa_sequences)
        
        # 创建伪MSA表示
        msa_repr = np.concatenate([
            pssm,  # (seq_len, 20)
            conservation.reshape(-1, 1),  # (seq_len, 1)
            np.random.randn(seq_len, max(1, self.msa_dim - 21)) * 0.1  # 填充到目标维度
        ], axis=1)
        
        features = {
            "msa_representation": torch.tensor(msa_repr, dtype=torch.float32).unsqueeze(0).unsqueeze(0),
            "query_representation": torch.tensor(msa_repr, dtype=torch.float32),
            "pssm": torch.tensor(pssm, dtype=torch.float32),
            "conservation_scores": torch.tensor(conservation, dtype=torch.float32),
            "coevolution_matrix": torch.tensor(coevolution, dtype=torch.float32)
        }
        
        return features
    
    def _calculate_pssm(self, sequences: List[str], seq_len: int) -> np.ndarray:
        """计算PSSM矩阵"""
        aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY")}
        pssm = np.zeros((seq_len, 20))
        
        for pos in range(seq_len):
            aa_counts = np.zeros(20)
            total_count = 0
            
            for seq in sequences:
                if pos < len(seq) and seq[pos] in aa_to_idx:
                    aa_counts[aa_to_idx[seq[pos]]] += 1
                    total_count += 1
            
            if total_count > 0:
                pssm[pos] = aa_counts / total_count
            else:
                pssm[pos] = np.ones(20) / 20
        
        return pssm
    
    def _calculate_conservation(self, sequences: List[str]) -> np.ndarray:
        """计算保守性分数"""
        if not sequences:
            return np.zeros(1)
            
        seq_len = len(sequences[0])
        conservation = np.zeros(seq_len)
        
        for pos in range(seq_len):
            aa_counts = {}
            for seq in sequences:
                if pos < len(seq):
                    aa = seq[pos]
                    aa_counts[aa] = aa_counts.get(aa, 0) + 1
            
            # 计算香农熵
            total = sum(aa_counts.values())
            entropy = 0
            for count in aa_counts.values():
                if count > 0:
                    p = count / total
                    entropy -= p * np.log2(p + 1e-8)
            
            conservation[pos] = -entropy
        
        return conservation
    
    def _calculate_coevolution(self, sequences: List[str]) -> np.ndarray:
        """计算共进化矩阵"""
        if not sequences:
            return np.zeros((1, 1))
            
        seq_len = len(sequences[0])
        coev_matrix = np.zeros((seq_len, seq_len))
        
        for i in range(seq_len):
            for j in range(i+1, seq_len):
                # 简化的互信息计算
                pair_counts = {}
                total = 0
                
                for seq in sequences:
                    if i < len(seq) and j < len(seq):
                        pair = (seq[i], seq[j])
                        pair_counts[pair] = pair_counts.get(pair, 0) + 1
                        total += 1
                
                mi = 0
                if total > 0:
                    for count in pair_counts.values():
                        if count > 0:
                            p = count / total
                            mi += p * np.log2(p + 1e-8)
                
                coev_matrix[i, j] = coev_matrix[j, i] = -mi
        
        return coev_matrix


class ProteinFeatureExtractor:
    """蛋白质特征提取器 - 修复版本"""
    
    def __init__(self, esm_model_name: str = "facebook/esm2_t33_650M_UR50D",
                 msa_model_name: str = "facebook/esm_msa1b_t12_100M_UR50S"):
        """
        初始化特征提取器
        
        Args:
            esm_model_name: ESM-2模型名称
            msa_model_name: MSA Transformer模型名称
        """
        self.esm_model_name = esm_model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载ESM-2模型 (使用fair-esm库)
        logger.info(f"Loading ESM-2 model: {esm_model_name}")
        try:
            # 使用fair-esm库加载模型
            if "esm2_t33_650M_UR50D" in esm_model_name:
                self.esm_model, self.esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()
            elif "esm2_t30_150M_UR50D" in esm_model_name:
                self.esm_model, self.esm_alphabet = esm.pretrained.esm2_t30_150M_UR50D()
            else:
                # 默认使用650M模型
                self.esm_model, self.esm_alphabet = esm.pretrained.esm2_t33_650M_UR50D()

            self.esm_model.to(self.device)
            self.esm_model.eval()
            self.esm_batch_converter = self.esm_alphabet.get_batch_converter()
            self.esm_dim = 1280  # ESM-2 t33模型的隐藏维度
            self.use_fair_esm = True

        except Exception as e:
            logger.error(f"Failed to load ESM-2 model with fair-esm: {e}")
            # 回退到transformers库
            try:
                self.esm_tokenizer = EsmTokenizer.from_pretrained(esm_model_name)
                self.esm_model = EsmModel.from_pretrained(esm_model_name)
                self.esm_model.to(self.device)
                self.esm_model.eval()
                self.esm_dim = self.esm_model.config.hidden_size
                self.use_fair_esm = False
            except Exception as e2:
                logger.error(f"Failed to load ESM-2 model with transformers: {e2}")
                raise e2
        
        # 初始化MSA特征提取器 - 使用修复版本
        self.msa_extractor = MSATransformerFeatureExtractor(msa_model_name)
        
    def extract_esm2_features(self, sequence: str) -> Dict[str, torch.Tensor]:
        """提取ESM-2特征"""
        # 确保序列是字符串且清理
        sequence = str(sequence).strip().upper()
        sequence = ''.join(c for c in sequence if c in 'ACDEFGHIKLMNPQRSTVWY')
        
        if not sequence:
            raise ValueError("Empty or invalid protein sequence")
        
        if self.use_fair_esm:
            # 使用fair-esm库
            data = [("protein", sequence)]
            batch_labels, batch_strs, batch_tokens = self.esm_batch_converter(data)
            batch_tokens = batch_tokens.to(self.device)

            with torch.no_grad():
                results = self.esm_model(batch_tokens, repr_layers=[33], return_contacts=True)

            # 提取特征
            token_representations = results["representations"][33]
            attention_maps = results["attentions"] if "attentions" in results else None

            features = {
                "residue_embeddings": token_representations[0, 1:-1, :],  # 移除BOS和EOS token
                "sequence_embedding": token_representations[0, 0, :],  # BOS token作为序列表示
                "attention_maps": attention_maps[0] if attention_maps is not None else None,
                "contacts": results.get("contacts", None),
            }

        else:
            # 使用transformers库
            inputs = self.esm_tokenizer(sequence, return_tensors="pt", padding=True, truncation=True, max_length=1024)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}

            with torch.no_grad():
                outputs = self.esm_model(**inputs, output_attentions=True, output_hidden_states=True)

            features = {
                "residue_embeddings": outputs.last_hidden_state[0, 1:-1, :],  # (L, D)
                "sequence_embedding": outputs.last_hidden_state[0, 0, :],  # (D,)
                "attention_maps": torch.stack([layer_attention[0] for layer_attention in outputs.attentions]),
                "hidden_states": torch.stack([hidden_state[0] for hidden_state in outputs.hidden_states]),
            }

            # 移除CLS和SEP token的注意力
            features["attention_maps"] = features["attention_maps"][:, :, 1:-1, 1:-1]
            features["hidden_states"] = features["hidden_states"][:, 1:-1, :]

        return features
    
    def generate_msa_with_hhblits(self, sequence: str, database_path: str = None) -> List[str]:
        """
        使用HHblits生成MSA - 改进版本
        """
        # 检查HHblits是否可用
        try:
            result = subprocess.run(["which", "hhblits"], capture_output=True, text=True)
            if result.returncode != 0:
                logger.warning("HHblits not found in PATH, using single sequence")
                return [sequence]
        except Exception:
            logger.warning("Cannot check HHblits availability, using single sequence")
            return [sequence]
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as query_file:
            query_file.write(f">query\n{sequence}\n")
            query_path = query_file.name
        
        msa_path = query_path.replace('.fasta', '.a3m')
        
        try:
            # 简化的HHblits命令
            cmd = ["hhblits", "-i", query_path, "-o", msa_path, "-n", "1", "-e", "1e-3"]
            
            # 如果没有指定数据库，尝试使用默认位置
            if not database_path:
                # 常见的HHblits数据库位置
                possible_dbs = [
                    "/usr/share/hhsuite/databases/uniclust30_2018_08/uniclust30_2018_08",
                    "/opt/hhsuite/databases/uniclust30_2018_08/uniclust30_2018_08",
                    "/data/databases/uniclust30_2018_08/uniclust30_2018_08"
                ]
                
                for db_path in possible_dbs:
                    if os.path.exists(f"{db_path}_a3m.ffdata"):
                        database_path = db_path
                        break
            
            if database_path:
                cmd.extend(["-d", database_path])
            else:
                logger.warning("No HHblits database found, using single sequence")
                return [sequence]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)  # 减少超时时间
            
            if result.returncode != 0:
                logger.warning(f"HHblits failed with return code {result.returncode}, using single sequence")
                if result.stderr:
                    logger.debug(f"HHblits stderr: {result.stderr}")
                return [sequence]
            
            # 读取MSA
            sequences = []
            if os.path.exists(msa_path):
                try:
                    with open(msa_path, 'r') as f:
                        for record in SeqIO.parse(f, "fasta"):
                            seq_str = str(record.seq).replace('-', '')
                            if seq_str:  # 确保序列不为空
                                sequences.append(seq_str)
                except Exception as e:
                    logger.warning(f"Failed to parse MSA file: {e}")
            
            return sequences if sequences else [sequence]
            
        except subprocess.TimeoutExpired:
            logger.warning("HHblits timeout, using single sequence")
            return [sequence]
        except Exception as e:
            logger.warning(f"MSA generation failed: {e}")
            return [sequence]
        
        finally:
            # 清理临时文件
            for path in [query_path, msa_path]:
                if os.path.exists(path):
                    try:
                        os.unlink(path)
                    except Exception:
                        pass
    
    def extract_features(self, sequence: str, msa_database_path: str = None) -> Dict[str, torch.Tensor]:
        """
        提取完整的蛋白质特征
        
        Args:
            sequence: 蛋白质序列
            msa_database_path: MSA数据库路径
            
        Returns:
            完整特征字典
        """
        logger.info("Extracting ESM-2 features...")
        esm_features = self.extract_esm2_features(sequence)
        
        logger.info("Generating MSA and extracting MSA features...")
        msa_sequences = self.generate_msa_with_hhblits(sequence, msa_database_path)
        msa_features = self.msa_extractor.extract_msa_transformer_features(msa_sequences)
        
        all_features = {
            "esm2": esm_features,
            "msa": msa_features,
            "sequence": sequence,
            "sequence_length": len(sequence),
            "msa_depth": len(msa_sequences)
        }
        
        return all_features
