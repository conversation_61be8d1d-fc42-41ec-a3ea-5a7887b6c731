"""
注意力融合蛋白质特征提取模型
实现基于交叉注意力机制的ESM-2和MSA特征融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List
import math
import logging
from einops import rearrange, repeat

logger = logging.getLogger(__name__)


class MultiHeadCrossAttention(nn.Module):
    """多头交叉注意力模块"""
    
    def __init__(self, query_dim: int, key_dim: int, value_dim: int, 
                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化多头交叉注意力
        
        Args:
            query_dim: 查询特征维度
            key_dim: 键特征维度
            value_dim: 值特征维度
            hidden_dim: 隐藏层维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.head_dim = hidden_dim // num_heads
        
        assert hidden_dim % num_heads == 0, "hidden_dim must be divisible by num_heads"
        
        # 线性投影层
        self.query_projection = nn.Linear(query_dim, hidden_dim)
        self.key_projection = nn.Linear(key_dim, hidden_dim)
        self.value_projection = nn.Linear(value_dim, hidden_dim)
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
        # 缩放因子
        self.scale = math.sqrt(self.head_dim)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor,
                mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            query: 查询张量 (batch_size, query_len, query_dim)
            key: 键张量 (batch_size, key_len, key_dim)
            value: 值张量 (batch_size, value_len, value_dim)
            mask: 注意力掩码
            
        Returns:
            输出特征和注意力权重
        """
        batch_size, query_len, _ = query.shape
        key_len = key.shape[1]
        
        # 线性投影
        Q = self.query_projection(query)  # (batch_size, query_len, hidden_dim)
        K = self.key_projection(key)      # (batch_size, key_len, hidden_dim)
        V = self.value_projection(value)  # (batch_size, value_len, hidden_dim)
        
        # 重塑为多头格式
        Q = rearrange(Q, 'b q (h d) -> b h q d', h=self.num_heads)
        K = rearrange(K, 'b k (h d) -> b h k d', h=self.num_heads)
        V = rearrange(V, 'b v (h d) -> b h v d', h=self.num_heads)
        
        # 计算注意力分数
        attention_scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # 应用掩码
        if mask is not None:
            attention_scores = attention_scores.masked_fill(mask == 0, -1e9)
        
        # Softmax归一化
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        attended_values = torch.matmul(attention_weights, V)
        
        # 重塑回原始格式
        output = rearrange(attended_values, 'b h q d -> b q (h d)')
        
        # 输出投影
        output = self.output_projection(output)
        
        return output, attention_weights.mean(dim=1)  # 平均多头注意力权重


class CrossAttentionBlock(nn.Module):
    """交叉注意力块"""
    
    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int, 
                 num_heads: int = 8, dropout: float = 0.1):
        """
        初始化交叉注意力块
        
        Args:
            esm_dim: ESM-2特征维度
            msa_dim: MSA特征维度
            hidden_dim: 隐藏层维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()
        
        # ESM-2查询MSA的交叉注意力 (投影后都是hidden_dim维度)
        self.esm_to_msa_attention = MultiHeadCrossAttention(
            query_dim=hidden_dim,
            key_dim=hidden_dim,
            value_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout
        )

        # MSA查询ESM-2的交叉注意力 (投影后都是hidden_dim维度)
        self.msa_to_esm_attention = MultiHeadCrossAttention(
            query_dim=hidden_dim,
            key_dim=hidden_dim,
            value_dim=hidden_dim,
            hidden_dim=hidden_dim,
            num_heads=num_heads,
            dropout=dropout
        )
        
        # 层归一化
        self.esm_norm1 = nn.LayerNorm(hidden_dim)
        self.esm_norm2 = nn.LayerNorm(hidden_dim)
        self.msa_norm1 = nn.LayerNorm(hidden_dim)
        self.msa_norm2 = nn.LayerNorm(hidden_dim)
        
        # 前馈网络
        self.esm_ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Dropout(dropout)
        )
        
        self.msa_ffn = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.Dropout(dropout)
        )
        
        # 特征投影层
        self.esm_input_projection = nn.Linear(esm_dim, hidden_dim)
        self.msa_input_projection = nn.Linear(msa_dim, hidden_dim)
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, Dict]:
        """
        前向传播
        
        Args:
            esm_features: ESM-2特征 (batch_size, seq_len, esm_dim)
            msa_features: MSA特征 (batch_size, seq_len, msa_dim)
            
        Returns:
            更新后的ESM-2特征、MSA特征和注意力权重
        """
        # 投影到相同维度
        esm_proj = self.esm_input_projection(esm_features)
        msa_proj = self.msa_input_projection(msa_features)
        
        # ESM-2查询MSA
        esm_attended, esm_to_msa_weights = self.esm_to_msa_attention(esm_proj, msa_proj, msa_proj)
        esm_updated = self.esm_norm1(esm_proj + esm_attended)
        esm_updated = self.esm_norm2(esm_updated + self.esm_ffn(esm_updated))
        
        # MSA查询ESM-2
        msa_attended, msa_to_esm_weights = self.msa_to_esm_attention(msa_proj, esm_proj, esm_proj)
        msa_updated = self.msa_norm1(msa_proj + msa_attended)
        msa_updated = self.msa_norm2(msa_updated + self.msa_ffn(msa_updated))
        
        attention_info = {
            "esm_to_msa_weights": esm_to_msa_weights,
            "msa_to_esm_weights": msa_to_esm_weights
        }
        
        return esm_updated, msa_updated, attention_info


class MSAFeatureProcessor(nn.Module):
    """MSA特征处理器"""
    
    def __init__(self, output_dim: int = 256):
        """
        初始化MSA特征处理器
        
        Args:
            output_dim: 输出特征维度
        """
        super().__init__()
        
        self.output_dim = output_dim
        
        # 如果有MSA Transformer特征，直接使用
        self.msa_transformer_projection = nn.Linear(768, output_dim)  # 假设MSA Transformer输出768维
        
        # 传统MSA特征处理
        self.pssm_processor = nn.Sequential(
            nn.Linear(20, 64),
            nn.ReLU(),
            nn.Linear(64, 128)
        )
        
        self.conservation_processor = nn.Sequential(
            nn.Linear(1, 32),
            nn.ReLU(),
            nn.Linear(32, 64)
        )
        
        self.traditional_fusion = nn.Sequential(
            nn.Linear(128 + 64, output_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_dim, output_dim)
        )
        
    def forward(self, msa_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        处理MSA特征
        
        Args:
            msa_features: MSA特征字典
            
        Returns:
            处理后的MSA特征
        """
        # 优先使用MSA Transformer特征
        if "query_representation" in msa_features:
            msa_feat = msa_features["query_representation"]
            if msa_feat.shape[-1] == 768:  # MSA Transformer特征
                return self.msa_transformer_projection(msa_feat)
        
        # 使用传统MSA特征
        if "pssm" in msa_features and "conservation_scores" in msa_features:
            pssm_feat = self.pssm_processor(msa_features["pssm"])
            conservation_feat = self.conservation_processor(
                msa_features["conservation_scores"].unsqueeze(-1)
            )
            combined_feat = torch.cat([pssm_feat, conservation_feat], dim=-1)
            return self.traditional_fusion(combined_feat)
        
        # 如果都没有，创建随机特征
        seq_len = msa_features.get("sequence_length", 100)
        return torch.randn(seq_len, self.output_dim)


class AttentionFusionModel(nn.Module):
    """注意力融合模型主类"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512,
                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化注意力融合模型
        
        Args:
            esm_dim: ESM-2特征维度
            msa_dim: MSA特征维度
            hidden_dim: 隐藏层维度
            num_blocks: 交叉注意力块数量
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.hidden_dim = hidden_dim
        self.num_blocks = num_blocks
        
        # MSA特征处理器
        self.msa_processor = MSAFeatureProcessor(msa_dim)
        
        # 交叉注意力块
        self.cross_attention_blocks = nn.ModuleList()
        for i in range(num_blocks):
            if i == 0:
                # 第一个块：处理原始维度
                block = CrossAttentionBlock(esm_dim, msa_dim, hidden_dim, num_heads, dropout)
            else:
                # 后续块：处理hidden_dim
                block = CrossAttentionBlock(hidden_dim, hidden_dim, hidden_dim, num_heads, dropout)
            self.cross_attention_blocks.append(block)
        
        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, hidden_dim)
        )
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1000, hidden_dim) * 0.1)
        
    def forward(self, esm_features: torch.Tensor, msa_features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            esm_features: ESM-2残基嵌入 (batch_size, seq_len, esm_dim)
            msa_features: MSA特征字典
            
        Returns:
            融合后的特征字典
        """
        batch_size, seq_len, _ = esm_features.shape
        
        # 移动MSA特征到正确设备
        device = esm_features.device
        msa_features_device = {}
        for k, v in msa_features.items():
            if isinstance(v, torch.Tensor):
                msa_features_device[k] = v.to(device)
            else:
                msa_features_device[k] = v

        # 处理MSA特征
        processed_msa = self.msa_processor(msa_features_device)  # (seq_len, msa_dim)
        processed_msa = processed_msa.unsqueeze(0).expand(batch_size, -1, -1)  # (batch_size, seq_len, msa_dim)
        
        # 初始化特征
        current_esm = esm_features
        current_msa = processed_msa
        
        # 存储所有注意力权重
        all_attention_weights = []
        
        # 通过交叉注意力块
        for i, block in enumerate(self.cross_attention_blocks):
            current_esm, current_msa, attention_info = block(current_esm, current_msa)
            all_attention_weights.append(attention_info)
            
            # 添加位置编码（仅在第一层）
            if i == 0 and seq_len <= self.pos_encoding.size(0):
                pos_enc = self.pos_encoding[:seq_len].unsqueeze(0)
                current_esm = current_esm + pos_enc
                current_msa = current_msa + pos_enc
        
        # 最终融合
        concatenated = torch.cat([current_esm, current_msa], dim=-1)
        fused_features = self.final_fusion(concatenated)
        
        return {
            "fused_features": fused_features,
            "enhanced_esm_features": current_esm,
            "enhanced_msa_features": current_msa,
            "attention_weights": all_attention_weights,
            "final_esm_contribution": self._compute_feature_contribution(current_esm, fused_features),
            "final_msa_contribution": self._compute_feature_contribution(current_msa, fused_features)
        }
    
    def _compute_feature_contribution(self, feature: torch.Tensor, fused: torch.Tensor) -> torch.Tensor:
        """计算特征对融合结果的贡献度"""
        # 使用余弦相似度计算贡献度
        similarity = F.cosine_similarity(feature, fused, dim=-1)
        return similarity.mean(dim=1)  # 平均每个序列的贡献度
    
    def extract_and_fuse(self, sequence: str, feature_extractor, msa_database_path: str = None) -> Dict[str, torch.Tensor]:
        """
        端到端特征提取和融合
        
        Args:
            sequence: 蛋白质序列
            feature_extractor: 特征提取器实例
            msa_database_path: MSA数据库路径
            
        Returns:
            融合后的特征
        """
        self.eval()
        
        with torch.no_grad():
            # 提取特征
            all_features = feature_extractor.extract_features(sequence, msa_database_path)
            
            # 准备输入
            esm_features = all_features["esm2"]["residue_embeddings"].unsqueeze(0)
            msa_features = all_features["msa"]
            
            # 移动到正确的设备
            device = next(self.parameters()).device
            esm_features = esm_features.to(device)
            msa_features = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                           for k, v in msa_features.items()}
            
            # 前向传播
            results = self.forward(esm_features, msa_features)
            
            # 添加原始特征信息
            results.update({
                "sequence": sequence,
                "sequence_length": len(sequence),
                "msa_depth": all_features.get("msa_depth", 1),
                "esm2_features": all_features["esm2"],
                "msa_features": all_features["msa"]
            })
            
            return results
    
    def get_attention_analysis(self, attention_weights: List[Dict]) -> Dict[str, torch.Tensor]:
        """
        分析注意力权重
        
        Args:
            attention_weights: 注意力权重列表
            
        Returns:
            注意力分析结果
        """
        # 计算平均注意力权重
        avg_esm_to_msa = torch.stack([w["esm_to_msa_weights"] for w in attention_weights]).mean(dim=0)
        avg_msa_to_esm = torch.stack([w["msa_to_esm_weights"] for w in attention_weights]).mean(dim=0)
        
        return {
            "average_esm_to_msa_attention": avg_esm_to_msa,
            "average_msa_to_esm_attention": avg_msa_to_esm,
            "attention_entropy": self._compute_attention_entropy(avg_esm_to_msa),
            "attention_sparsity": self._compute_attention_sparsity(avg_esm_to_msa)
        }
    
    def _compute_attention_entropy(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """计算注意力熵"""
        # 避免log(0)
        attention_weights = attention_weights + 1e-8
        entropy = -(attention_weights * torch.log(attention_weights)).sum(dim=-1)
        return entropy
    
    def _compute_attention_sparsity(self, attention_weights: torch.Tensor) -> torch.Tensor:
        """计算注意力稀疏性"""
        # 计算每行的基尼系数作为稀疏性度量
        sorted_weights, _ = torch.sort(attention_weights, dim=-1, descending=True)
        n = sorted_weights.size(-1)
        index = torch.arange(1, n + 1, dtype=torch.float, device=sorted_weights.device)
        gini = (2 * (sorted_weights * index).sum(dim=-1)) / (n * sorted_weights.sum(dim=-1)) - (n + 1) / n
        return gini
