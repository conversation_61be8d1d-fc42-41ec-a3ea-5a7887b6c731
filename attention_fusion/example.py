"""
注意力融合模型使用示例
"""

import torch
import logging
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel
from utils import (
    visualize_attention_weights,
    visualize_cross_attention_flow,
    analyze_attention_patterns,
    print_attention_summary,
    validate_attention_fusion_input
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """主函数，演示注意力融合模型的使用"""
    
    # 示例蛋白质序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL"
    ]
    
    print("=" * 80)
    print("注意力融合蛋白质特征提取模型示例")
    print("=" * 80)
    
    # 1. 初始化特征提取器
    print("\n1. 初始化特征提取器...")
    try:
        extractor = ProteinFeatureExtractor(
            esm_model_name="facebook/esm2_t33_650M_UR50D",
            msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
        )
        print("✓ 特征提取器初始化成功")
    except Exception as e:
        logger.error(f"特征提取器初始化失败: {e}")
        return
    
    # 2. 初始化注意力融合模型
    print("\n2. 初始化注意力融合模型...")
    model = AttentionFusionModel(
        esm_dim=1280,      # ESM-2 t33模型的特征维度
        msa_dim=256,       # MSA特征维度
        hidden_dim=512,    # 隐藏层维度
        num_blocks=4,      # 交叉注意力块数量
        num_heads=8,       # 注意力头数
        dropout=0.1        # Dropout率
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")
    
    # 3. 处理示例序列
    for i, sequence in enumerate(test_sequences):
        print(f"\n{'='*60}")
        print(f"处理序列 {i+1}: {sequence[:30]}...")
        print(f"序列长度: {len(sequence)}")
        
        try:
            # 提取和融合特征
            print("正在提取ESM-2和MSA特征...")
            results = model.extract_and_fuse(sequence, extractor)
            
            print("✓ 特征提取和融合完成")
            
            # 打印结果摘要
            print_attention_summary(results)
            
            # 分析注意力模式
            if "attention_weights" in results:
                attention_analysis = analyze_attention_patterns(results["attention_weights"], sequence)
                print("\n注意力模式分析:")
                for key, value in attention_analysis.items():
                    print(f"  {key}: {value:.4f}")
            
            # 可视化注意力权重（仅对较短序列）
            if len(sequence) <= 80 and "attention_weights" in results:
                print("\n生成注意力可视化...")
                try:
                    attention_weights = results["attention_weights"]
                    if attention_weights:
                        first_layer = attention_weights[0]
                        
                        # 可视化ESM-2到MSA的注意力
                        if "esm_to_msa_weights" in first_layer:
                            visualize_attention_weights(
                                first_layer["esm_to_msa_weights"][0],
                                sequence,
                                title=f"ESM-2 → MSA Attention (Seq {i+1})",
                                save_path=f"esm_to_msa_attention_seq_{i+1}.png"
                            )
                        
                        # 可视化交叉注意力流
                        if "esm_to_msa_weights" in first_layer and "msa_to_esm_weights" in first_layer:
                            visualize_cross_attention_flow(
                                first_layer["esm_to_msa_weights"][0],
                                first_layer["msa_to_esm_weights"][0],
                                sequence,
                                save_path=f"cross_attention_flow_seq_{i+1}.png"
                            )
                    
                    print("✓ 注意力可视化已保存")
                except Exception as e:
                    print(f"⚠ 可视化生成失败: {e}")
            
            # 特征质量分析
            print("\n特征质量分析:")
            fused_features = results["fused_features"]
            print(f"  融合特征形状: {fused_features.shape}")
            print(f"  特征均值: {fused_features.mean().item():.4f}")
            print(f"  特征标准差: {fused_features.std().item():.4f}")
            
            if "enhanced_esm_features" in results and "enhanced_msa_features" in results:
                esm_enhanced = results["enhanced_esm_features"]
                msa_enhanced = results["enhanced_msa_features"]
                
                # 计算特征相似性
                from utils import compute_feature_similarity
                similarity = compute_feature_similarity(esm_enhanced[0], msa_enhanced[0])
                print(f"  增强后ESM-2与MSA特征相似性: {similarity['mean_similarity']:.4f}")
            
        except Exception as e:
            logger.error(f"处理序列 {i+1} 时出错: {e}")
            continue
    
    # 4. 演示模型的高级功能
    print(f"\n{'='*60}")
    print("高级功能演示")
    print("="*60)
    
    demonstrate_attention_mechanisms(model)
    
    print(f"\n{'='*80}")
    print("示例运行完成！")
    print("=" * 80)


def demonstrate_attention_mechanisms(model):
    """演示注意力机制的工作原理"""
    
    print("\n1. 注意力机制测试...")
    
    # 创建测试数据
    batch_size, seq_len = 1, 50
    esm_dim, msa_dim = 1280, 256
    
    # 模拟输入数据
    esm_features = torch.randn(batch_size, seq_len, esm_dim)
    msa_features = {
        "query_representation": torch.randn(seq_len, msa_dim),
        "pssm": torch.randn(seq_len, 20),
        "conservation_scores": torch.randn(seq_len)
    }
    
    # 验证输入
    if not validate_attention_fusion_input(esm_features, msa_features):
        print("❌ 输入验证失败")
        return
    
    print("✓ 输入验证通过")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        results = model(esm_features, msa_features)
    
    print("✓ 前向传播完成")
    
    # 分析注意力权重
    if "attention_weights" in results:
        attention_weights = results["attention_weights"]
        print(f"  注意力层数: {len(attention_weights)}")
        
        # 分析第一层注意力
        first_layer = attention_weights[0]
        if "esm_to_msa_weights" in first_layer:
            esm_to_msa = first_layer["esm_to_msa_weights"]
            print(f"  ESM-2→MSA注意力形状: {esm_to_msa.shape}")
            print(f"  ESM-2→MSA平均注意力: {esm_to_msa.mean().item():.4f}")
            print(f"  ESM-2→MSA最大注意力: {esm_to_msa.max().item():.4f}")
        
        if "msa_to_esm_weights" in first_layer:
            msa_to_esm = first_layer["msa_to_esm_weights"]
            print(f"  MSA→ESM-2注意力形状: {msa_to_esm.shape}")
            print(f"  MSA→ESM-2平均注意力: {msa_to_esm.mean().item():.4f}")
            print(f"  MSA→ESM-2最大注意力: {msa_to_esm.max().item():.4f}")
    
    # 分析特征贡献
    if "final_esm_contribution" in results and "final_msa_contribution" in results:
        esm_contrib = results["final_esm_contribution"].mean().item()
        msa_contrib = results["final_msa_contribution"].mean().item()
        print(f"  最终ESM-2贡献度: {esm_contrib:.4f}")
        print(f"  最终MSA贡献度: {msa_contrib:.4f}")
    
    print("\n2. 注意力分析...")
    
    # 使用模型的注意力分析功能
    if "attention_weights" in results:
        attention_analysis = model.get_attention_analysis(results["attention_weights"])
        
        for key, value in attention_analysis.items():
            if isinstance(value, torch.Tensor):
                if value.numel() == 1:
                    print(f"  {key}: {value.item():.4f}")
                else:
                    print(f"  {key} (mean): {value.mean().item():.4f}")
    
    print("✓ 高级功能演示完成")


def compare_fusion_methods():
    """比较不同融合方法的效果"""
    
    print("\n" + "="*60)
    print("融合方法比较")
    print("="*60)
    
    # 这里可以添加与门控融合和架构级融合的比较代码
    # 由于篇幅限制，这里只是一个框架
    
    print("注意力融合的优势:")
    print("  ✓ 能够捕获复杂的特征间关系")
    print("  ✓ 提供可解释的注意力权重")
    print("  ✓ 支持多层特征交互")
    print("  ✓ 适用于各种下游任务")
    
    print("\n注意力融合的考虑:")
    print("  ⚠ 计算复杂度较高")
    print("  ⚠ 需要更多的训练数据")
    print("  ⚠ 参数量较大")


if __name__ == "__main__":
    main()
    compare_fusion_methods()
