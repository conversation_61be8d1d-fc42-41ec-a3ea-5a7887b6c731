#!/usr/bin/env python3
"""
批量处理Km_Data.xlsx文件，支持断点续传和批量保存
"""

import pandas as pd
import numpy as np
import torch
import os
import sys
from pathlib import Path
import logging
from tqdm import tqdm
import json
from datetime import datetime

# 导入attention_fusion模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BatchProcessor:
    def __init__(self, batch_size=50):
        self.batch_size = batch_size
        self.output_file = "../20250623_Data/Km_Data_fusion_vector.csv"
        self.progress_file = "../20250623_Data/processing_progress.json"
        self.device = None
        self.extractor = None
        self.model = None
        
    def setup_models(self):
        """初始化模型"""
        logger.info("初始化attention_fusion模型...")
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger.info(f"使用设备: {self.device}")
        
        # 初始化特征提取器
        self.extractor = ProteinFeatureExtractor(
            esm_model_name="facebook/esm2_t33_650M_UR50D",
            msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
        )
        
        # 初始化融合模型
        self.model = AttentionFusionModel(
            esm_dim=1280, 
            msa_dim=768,
            hidden_dim=512, 
            num_blocks=2, 
            num_heads=4
        )
        self.model.to(self.device)
        self.model.eval()
        
        logger.info("✅ 模型初始化完成")
    
    def extract_fusion_features(self, sequence):
        """提取单个序列的融合特征"""
        try:
            # 检查序列长度
            if len(sequence) > 1000:
                logger.warning(f"序列长度 {len(sequence)} 超过1000，截取前1000个氨基酸")
                sequence = sequence[:1000]
            
            # 提取融合特征
            results = self.model.extract_and_fuse(sequence, self.extractor)
            
            # 获取融合特征 (batch_size, seq_len, hidden_dim)
            fused_features = results["fused_features"]  # [1, seq_len, 512]
            
            # 计算序列级表示 (平均池化)
            sequence_representation = fused_features.mean(dim=1).squeeze(0)  # [512]
            
            # 转换为numpy数组
            feature_vector = sequence_representation.detach().cpu().numpy()
            
            return feature_vector, True, None
            
        except Exception as e:
            logger.error(f"处理序列时出错: {str(e)}")
            return None, False, str(e)
    
    def load_progress(self):
        """加载处理进度"""
        if os.path.exists(self.progress_file):
            with open(self.progress_file, 'r') as f:
                progress = json.load(f)
            logger.info(f"从进度文件恢复: 已处理 {progress['processed_count']} 个序列")
            return progress
        return {"processed_count": 0, "last_processed_index": -1}
    
    def save_progress(self, processed_count, last_index):
        """保存处理进度"""
        progress = {
            "processed_count": processed_count,
            "last_processed_index": last_index,
            "last_update": datetime.now().isoformat()
        }
        with open(self.progress_file, 'w') as f:
            json.dump(progress, f, indent=2)
    
    def load_existing_results(self):
        """加载已有的结果文件"""
        if os.path.exists(self.output_file):
            try:
                df = pd.read_csv(self.output_file)
                logger.info(f"加载已有结果文件: {len(df)} 条记录")
                return df
            except Exception as e:
                logger.warning(f"无法加载已有结果文件: {e}")
        return pd.DataFrame()
    
    def append_results_to_file(self, batch_results):
        """将批次结果追加到文件"""
        # 准备数据
        batch_data = []
        for result in batch_results:
            row = {
                'ID': result['ID'],
                'Sequence': result['Sequence'],
                'success': result['success']
            }
            
            if result['success'] and result['fusion_vector'] is not None:
                # 将特征向量转换为字符串格式保存
                feature_vector = result['fusion_vector']
                row['fusion_vector'] = ','.join(map(str, feature_vector))
                row['vector_dim'] = len(feature_vector)
                row['error'] = None
            else:
                row['fusion_vector'] = None
                row['vector_dim'] = None
                row['error'] = result.get('error', 'Unknown error')
            
            batch_data.append(row)
        
        # 创建DataFrame
        batch_df = pd.DataFrame(batch_data)
        
        # 追加到文件
        if os.path.exists(self.output_file):
            # 文件存在，追加数据
            batch_df.to_csv(self.output_file, mode='a', header=False, index=False)
        else:
            # 文件不存在，创建新文件
            batch_df.to_csv(self.output_file, index=False)
        
        logger.info(f"✅ 已保存 {len(batch_data)} 条结果到文件")
    
    def process_batch(self, batch_data, start_idx):
        """处理一个批次的数据"""
        batch_results = []
        
        for i, (idx, row) in enumerate(batch_data.iterrows()):
            seq_id = row['ID']
            sequence = row['Sequence']
            
            # 检查序列是否有效
            if pd.isna(sequence) or len(sequence) < 8:
                logger.warning(f"序列 {seq_id} 无效，跳过")
                batch_results.append({
                    'ID': seq_id,
                    'Sequence': sequence,
                    'fusion_vector': None,
                    'success': False,
                    'error': 'Invalid sequence'
                })
                continue
            
            # 提取特征
            feature_vector, success, error = self.extract_fusion_features(sequence)
            
            batch_results.append({
                'ID': seq_id,
                'Sequence': sequence,
                'fusion_vector': feature_vector,
                'success': success,
                'error': error
            })
            
            # 显示进度
            current_idx = start_idx + i + 1
            if current_idx % 10 == 0:
                logger.info(f"批次内进度: {i+1}/{len(batch_data)}, 总进度: {current_idx}")
        
        return batch_results
    
    def process_all_data(self):
        """处理所有数据"""
        logger.info("开始批量处理Km_Data.xlsx文件")
        
        # 读取数据
        input_file = "../20250623_Data/Km_Data.xlsx"
        if not os.path.exists(input_file):
            logger.error(f"输入文件不存在: {input_file}")
            return
        
        logger.info("读取Excel文件...")
        df = pd.read_excel(input_file)
        logger.info(f"数据形状: {df.shape}")
        
        # 提取ID和Sequence列
        if 'ID' not in df.columns or 'Sequence' not in df.columns:
            logger.error("数据文件中缺少ID或Sequence列")
            return
        
        work_df = df[['ID', 'Sequence']].copy()
        total_sequences = len(work_df)
        logger.info(f"总共需要处理 {total_sequences} 条序列")
        
        # 加载进度
        progress = self.load_progress()
        start_index = progress['last_processed_index'] + 1
        
        if start_index > 0:
            logger.info(f"从第 {start_index + 1} 条序列开始处理")
        
        # 初始化模型
        self.setup_models()
        
        # 批量处理
        processed_count = progress['processed_count']
        
        for batch_start in range(start_index, total_sequences, self.batch_size):
            batch_end = min(batch_start + self.batch_size, total_sequences)
            batch_data = work_df.iloc[batch_start:batch_end]
            
            logger.info(f"处理批次 {batch_start+1}-{batch_end} ({len(batch_data)} 条序列)")
            
            try:
                # 处理当前批次
                batch_results = self.process_batch(batch_data, batch_start)
                
                # 保存批次结果
                self.append_results_to_file(batch_results)
                
                # 更新进度
                processed_count += len(batch_results)
                self.save_progress(processed_count, batch_end - 1)
                
                # 统计成功率
                successful = sum(1 for r in batch_results if r['success'])
                success_rate = successful / len(batch_results) * 100
                
                logger.info(f"批次完成: 成功 {successful}/{len(batch_results)} ({success_rate:.1f}%)")
                logger.info(f"总进度: {processed_count}/{total_sequences} ({processed_count/total_sequences*100:.1f}%)")
                
            except Exception as e:
                logger.error(f"处理批次时出错: {e}")
                logger.info("保存当前进度并退出")
                self.save_progress(processed_count, batch_start - 1)
                break
        
        # 处理完成
        if processed_count >= total_sequences:
            logger.info("🎉 所有数据处理完成!")
            
            # 生成统计信息
            self.generate_statistics()
            
            # 清理进度文件
            if os.path.exists(self.progress_file):
                os.remove(self.progress_file)
                logger.info("已清理进度文件")
        else:
            logger.info(f"处理中断，已完成 {processed_count}/{total_sequences} 条序列")
            logger.info("可以重新运行脚本继续处理")
    
    def generate_statistics(self):
        """生成统计信息"""
        if not os.path.exists(self.output_file):
            return
        
        df = pd.read_csv(self.output_file)
        
        stats = {
            'total_sequences': len(df),
            'successful': len(df[df['success'] == True]),
            'failed': len(df[df['success'] == False]),
            'success_rate': len(df[df['success'] == True]) / len(df) * 100,
            'feature_dimension': 512,
            'processing_date': datetime.now().isoformat()
        }
        
        stats_file = self.output_file.replace('.csv', '_stats.json')
        with open(stats_file, 'w') as f:
            json.dump(stats, f, indent=2)
        
        logger.info("统计信息:")
        logger.info(f"  总序列数: {stats['total_sequences']}")
        logger.info(f"  成功处理: {stats['successful']}")
        logger.info(f"  处理失败: {stats['failed']}")
        logger.info(f"  成功率: {stats['success_rate']:.1f}%")
        logger.info(f"  特征维度: {stats['feature_dimension']}")

def main():
    """主函数"""
    # 创建处理器
    processor = BatchProcessor(batch_size=50)  # 每50个序列保存一次
    
    # 处理所有数据
    processor.process_all_data()

if __name__ == "__main__":
    main()
