# 注意力融合蛋白质特征提取方法

## 概述

本模块实现了基于交叉注意力机制的ESM-2和MSA特征融合方法，通过让一种特征作为查询去关注另一种特征，实现精细化的特征交互和对齐。

## 特点

- **交叉注意力**: 使用ESM-2特征作为查询，去关注和提取MSA中的相关信息
- **强大能力**: 能够捕获特征间复杂的非线性关系
- **SOTA级别**: 采用当前最先进模型的标配方法

## 文件结构

```
attention_fusion/
├── README.md                 # 使用说明
├── requirements.txt          # 依赖包
├── feature_extractor.py      # 特征提取器
├── attention_fusion_model.py # 注意力融合模型
├── utils.py                  # 工具函数
└── example.py               # 使用示例
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

```python
from attention_fusion_model import AttentionFusionModel
from feature_extractor import ProteinFeatureExtractor

# 初始化特征提取器和模型
extractor = ProteinFeatureExtractor()
model = AttentionFusionModel(esm_dim=1280, msa_dim=256, num_heads=8)

# 提取特征并融合
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
fused_features = model.extract_and_fuse(sequence, extractor)
```

## 详细使用说明

### 1. 环境配置

确保您的系统满足以下要求：
- Python 3.8+
- CUDA 11.0+ (推荐，用于GPU加速)
- 至少16GB内存 (注意力机制计算量较大)

### 2. 安装步骤

```bash
# 进入目录
cd attention_fusion

# 安装依赖
pip install -r requirements.txt

# 安装额外依赖
pip install einops  # 用于张量操作

# 可选：安装HHblits和MSA Transformer
# 参考官方文档安装
```

### 3. 基本使用

#### 3.1 简单示例

```python
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel

# 初始化（支持MSA Transformer）
extractor = ProteinFeatureExtractor(
    esm_model_name="facebook/esm2_t33_650M_UR50D",
    msa_model_name="facebook/esm_msa1b_t12_100M_UR50S"
)

model = AttentionFusionModel(
    esm_dim=1280,
    msa_dim=256,
    hidden_dim=512,
    num_blocks=4,
    num_heads=8
)

# 处理序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
results = model.extract_and_fuse(sequence, extractor)

# 获取结果
fused_features = results["fused_features"]           # 融合特征
attention_weights = results["attention_weights"]     # 注意力权重
enhanced_esm = results["enhanced_esm_features"]      # 增强的ESM-2特征
enhanced_msa = results["enhanced_msa_features"]      # 增强的MSA特征
```

#### 3.2 注意力分析

```python
from utils import analyze_attention_patterns, visualize_attention_weights

# 分析注意力模式
attention_analysis = analyze_attention_patterns(results["attention_weights"], sequence)
print(f"ESM-2到MSA注意力集中度: {attention_analysis['esm_to_msa_concentration']:.3f}")

# 可视化注意力权重
if len(sequence) <= 80:
    first_layer = results["attention_weights"][0]
    visualize_attention_weights(
        first_layer["esm_to_msa_weights"][0],
        sequence,
        title="ESM-2 → MSA Attention",
        save_path="esm_to_msa_attention.png"
    )
```

#### 3.3 交叉注意力可视化

```python
from utils import visualize_cross_attention_flow

# 可视化交叉注意力流
first_layer = results["attention_weights"][0]
visualize_cross_attention_flow(
    first_layer["esm_to_msa_weights"][0],
    first_layer["msa_to_esm_weights"][0],
    sequence,
    save_path="cross_attention_flow.png"
)
```

### 4. 参数说明

#### 4.1 AttentionFusionModel参数

- `esm_dim` (int): ESM-2特征维度，默认1280
- `msa_dim` (int): MSA特征维度，默认256
- `hidden_dim` (int): 隐藏层维度，默认512
- `num_blocks` (int): 交叉注意力块数量，默认4
- `num_heads` (int): 注意力头数，默认8
- `dropout` (float): Dropout率，默认0.1

#### 4.2 CrossAttentionBlock参数

- `esm_dim` (int): ESM-2特征维度
- `msa_dim` (int): MSA特征维度
- `hidden_dim` (int): 隐藏层维度
- `num_heads` (int): 注意力头数
- `dropout` (float): Dropout率

### 5. 输出说明

模型输出包含以下字段：

- `fused_features`: 最终融合特征 (batch_size, seq_len, hidden_dim)
- `enhanced_esm_features`: 增强的ESM-2特征 (batch_size, seq_len, hidden_dim)
- `enhanced_msa_features`: 增强的MSA特征 (batch_size, seq_len, hidden_dim)
- `attention_weights`: 各层注意力权重列表
- `final_esm_contribution`: ESM-2最终贡献度
- `final_msa_contribution`: MSA最终贡献度

### 6. 高级功能

#### 6.1 注意力权重分析

```python
# 获取注意力分析
attention_analysis = model.get_attention_analysis(results["attention_weights"])

print(f"平均注意力熵: {attention_analysis['attention_entropy'].mean():.3f}")
print(f"注意力稀疏性: {attention_analysis['attention_sparsity'].mean():.3f}")
```

#### 6.2 特征相似性分析

```python
from utils import compute_feature_similarity

# 计算特征相似性
similarity = compute_feature_similarity(
    results["enhanced_esm_features"][0],
    results["enhanced_msa_features"][0],
    method="cosine"
)
print(f"增强特征相似性: {similarity['mean_similarity']:.3f}")
```

#### 6.3 多层特征演化

```python
from utils import plot_feature_evolution

# 提取各层特征
esm_evolution = [block_result["enhanced_esm_features"] for block_result in layer_results]
plot_feature_evolution(esm_evolution, sequence, save_path="esm_evolution.png")
```

### 7. 性能优化

#### 7.1 内存优化

```python
# 使用梯度检查点减少内存使用
import torch.utils.checkpoint as checkpoint

# 在模型中启用梯度检查点
model.use_checkpoint = True
```

#### 7.2 计算优化

```python
# 减少注意力头数或块数
model = AttentionFusionModel(
    esm_dim=1280,
    msa_dim=256,
    hidden_dim=256,  # 减少隐藏层维度
    num_blocks=2,    # 减少块数
    num_heads=4      # 减少注意力头数
)
```

### 8. 故障排除

#### 8.1 常见问题

**Q: 注意力计算内存溢出**
A: 减少序列长度、注意力头数或使用CPU计算

**Q: MSA Transformer加载失败**
A: 检查网络连接，或使用传统MSA特征作为备选

**Q: 注意力权重异常**
A: 检查输入特征的数值范围，确保没有NaN或Inf值

#### 8.2 调试技巧

```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 检查注意力权重
attention_weights = results["attention_weights"][0]["esm_to_msa_weights"]
print(f"注意力权重范围: [{attention_weights.min():.4f}, {attention_weights.max():.4f}]")
print(f"注意力权重和: {attention_weights.sum(dim=-1).mean():.4f}")  # 应该接近1.0
```

### 9. 最佳实践

#### 9.1 序列长度建议

- 短序列 (<100): 使用完整参数
- 中等序列 (100-300): 适当减少注意力头数
- 长序列 (>300): 考虑序列分段或使用其他方法

#### 9.2 MSA质量要求

- 推荐MSA深度 >50 序列
- 序列同源性 20-90%
- 避免过度冗余的MSA

### 10. 引用

```bibtex
@article{attention_fusion,
  title={Cross-Attention Fusion of ESM-2 and MSA Transformer Features},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```

---

更多详细示例和高级用法请参考 `example.py` 文件。
