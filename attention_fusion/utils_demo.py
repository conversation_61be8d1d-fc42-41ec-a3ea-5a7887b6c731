#!/usr/bin/env python3
"""
utils.py 功能演示
展示所有工具函数的使用方法
"""

import torch
import numpy as np
import os
from pathlib import Path

# 导入项目模块
from feature_extractor import ProteinFeatureExtractor
from attention_fusion_model import AttentionFusionModel
import utils

def demo_basic_usage():
    """演示基本使用流程"""
    
    print("🧬 Utils.py 基本使用演示")
    print("=" * 50)
    
    # 1. 初始化模型
    print("1. 初始化模型...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    extractor = ProteinFeatureExtractor()
    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)
    model.to(device)
    
    # 2. 准备测试序列
    test_sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
    print(f"2. 测试序列长度: {len(test_sequence)}")
    
    # 3. 提取特征和运行模型
    print("3. 提取特征...")
    all_features = extractor.extract_features(test_sequence)
    esm_features = all_features['esm2']['residue_embeddings'].unsqueeze(0).to(device)
    msa_features = all_features['msa']
    
    print("4. 运行注意力融合模型...")
    results = model(esm_features, msa_features)
    
    return results, test_sequence, esm_features, msa_features

def demo_input_validation():
    """演示输入验证功能"""
    
    print("\n🔍 输入验证演示")
    print("=" * 30)
    
    # 准备测试数据
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 有效输入
    valid_esm = torch.randn(1, 50, 1280).to(device)
    valid_msa = {
        "query_representation": torch.randn(50, 768),
        "pssm": torch.randn(50, 20),
        "conservation_scores": torch.randn(50)
    }
    
    # 无效输入
    invalid_esm_shape = torch.randn(50, 1280)  # 缺少batch维度
    invalid_msa_empty = {}
    invalid_msa_type = "not_a_dict"
    
    print("测试有效输入:")
    is_valid = utils.validate_attention_fusion_input(valid_esm, valid_msa)
    print(f"  结果: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    print("测试无效ESM特征形状:")
    is_valid = utils.validate_attention_fusion_input(invalid_esm_shape, valid_msa)
    print(f"  结果: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    print("测试空MSA特征:")
    is_valid = utils.validate_attention_fusion_input(valid_esm, invalid_msa_empty)
    print(f"  结果: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    print("测试错误MSA类型:")
    is_valid = utils.validate_attention_fusion_input(valid_esm, invalid_msa_type)
    print(f"  结果: {'✅ 有效' if is_valid else '❌ 无效'}")

def demo_attention_analysis():
    """演示注意力分析功能"""
    
    print("\n📊 注意力分析演示")
    print("=" * 30)
    
    # 获取模型结果
    results, sequence, esm_features, msa_features = demo_basic_usage()
    
    # 1. 打印注意力摘要
    print("1. 注意力分析摘要:")
    utils.print_attention_summary(results)
    
    # 2. 详细注意力模式分析
    print("\n2. 详细注意力模式分析:")
    if "attention_weights" in results:
        attention_weights = results["attention_weights"]
        analysis = utils.analyze_attention_patterns(attention_weights, sequence)
        
        print("   注意力模式分析结果:")
        for key, value in analysis.items():
            print(f"     {key}: {value:.4f}")
    
    return results, sequence

def demo_feature_similarity():
    """演示特征相似性分析"""
    
    print("\n🔗 特征相似性分析演示")
    print("=" * 30)
    
    # 获取模型结果
    results, sequence, esm_features, msa_features = demo_basic_usage()
    
    # 比较不同特征
    fused_features = results["fused_features"]
    enhanced_esm = results["enhanced_esm_features"]
    enhanced_msa = results["enhanced_msa_features"]
    
    # 1. 余弦相似性
    print("1. 余弦相似性分析:")
    
    # 融合特征 vs 增强ESM特征
    sim_fused_esm = utils.compute_feature_similarity(fused_features, enhanced_esm, method="cosine")
    print("   融合特征 vs 增强ESM特征:")
    for key, value in sim_fused_esm.items():
        print(f"     {key}: {value:.4f}")
    
    # 融合特征 vs 增强MSA特征
    sim_fused_msa = utils.compute_feature_similarity(fused_features, enhanced_msa, method="cosine")
    print("   融合特征 vs 增强MSA特征:")
    for key, value in sim_fused_msa.items():
        print(f"     {key}: {value:.4f}")
    
    # 2. L2距离
    print("\n2. L2距离分析:")
    
    dist_fused_esm = utils.compute_feature_similarity(fused_features, enhanced_esm, method="l2")
    print("   融合特征 vs 增强ESM特征:")
    for key, value in dist_fused_esm.items():
        print(f"     {key}: {value:.4f}")

def demo_visualization():
    """演示可视化功能"""
    
    print("\n🎨 可视化功能演示")
    print("=" * 30)
    
    # 获取模型结果
    results, sequence, esm_features, msa_features = demo_basic_usage()
    
    # 创建输出目录
    output_dir = Path("visualization_output")
    output_dir.mkdir(exist_ok=True)
    
    print("1. 注意力权重可视化...")
    
    if "attention_weights" in results and results["attention_weights"]:
        attention_weights = results["attention_weights"]
        
        # 可视化第一层的注意力权重
        first_layer = attention_weights[0]
        
        if "esm_to_msa_weights" in first_layer:
            esm_to_msa = first_layer["esm_to_msa_weights"].squeeze(0)  # 移除batch维度
            
            print("   - ESM-2到MSA注意力权重热图")
            utils.visualize_attention_weights(
                esm_to_msa, 
                sequence, 
                title="ESM-2 to MSA Attention (Layer 1)",
                save_path=str(output_dir / "esm_to_msa_attention.png")
            )
        
        if "msa_to_esm_weights" in first_layer:
            msa_to_esm = first_layer["msa_to_esm_weights"].squeeze(0)
            
            print("   - MSA到ESM-2注意力权重热图")
            utils.visualize_attention_weights(
                msa_to_esm, 
                sequence, 
                title="MSA to ESM-2 Attention (Layer 1)",
                save_path=str(output_dir / "msa_to_esm_attention.png")
            )
        
        # 交叉注意力流可视化
        if "esm_to_msa_weights" in first_layer and "msa_to_esm_weights" in first_layer:
            print("   - 交叉注意力流可视化")
            utils.visualize_cross_attention_flow(
                first_layer["esm_to_msa_weights"].squeeze(0),
                first_layer["msa_to_esm_weights"].squeeze(0),
                sequence,
                save_path=str(output_dir / "cross_attention_flow.png")
            )
    
    # 特征演化可视化
    print("2. 特征演化可视化...")
    
    # 收集不同层的特征
    features_by_layer = [
        results["enhanced_esm_features"].squeeze(0),  # 第一层输出
        results["fused_features"].squeeze(0)          # 最终融合特征
    ]
    
    utils.plot_feature_evolution(
        features_by_layer,
        sequence,
        save_path=str(output_dir / "feature_evolution.png")
    )
    
    print(f"   所有可视化结果保存在: {output_dir}")

def demo_save_load():
    """演示保存和加载功能"""
    
    print("\n💾 保存和加载演示")
    print("=" * 30)
    
    # 获取模型结果
    results, sequence, esm_features, msa_features = demo_basic_usage()
    
    # 创建输出目录
    output_dir = Path("analysis_output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存分析结果
    save_path = str(output_dir / "attention_analysis.npz")
    print(f"1. 保存分析结果到: {save_path}")
    utils.save_attention_analysis(results, save_path)
    
    # 加载分析结果
    print("2. 加载分析结果...")
    loaded_results = utils.load_attention_analysis(save_path)
    
    print("3. 验证加载的数据:")
    for key in ["fused_features", "enhanced_esm_features", "enhanced_msa_features"]:
        if key in loaded_results:
            original_shape = results[key].shape
            loaded_shape = loaded_results[key].shape
            print(f"   {key}: 原始 {original_shape} -> 加载 {loaded_shape}")
            
            # 验证数据一致性
            if torch.allclose(results[key].cpu(), loaded_results[key], atol=1e-6):
                print(f"     ✅ 数据一致")
            else:
                print(f"     ❌ 数据不一致")

def demo_advanced_analysis():
    """演示高级分析功能"""
    
    print("\n🔬 高级分析演示")
    print("=" * 30)
    
    # 比较不同序列的注意力模式
    sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL"[:100]  # 截取前100个氨基酸
    ]
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    extractor = ProteinFeatureExtractor()
    model = AttentionFusionModel(esm_dim=1280, msa_dim=768, hidden_dim=512, num_blocks=2, num_heads=4)
    model.to(device)
    
    print("1. 比较不同序列的注意力模式:")
    
    all_analyses = []
    for i, seq in enumerate(sequences):
        print(f"\n   序列 {i+1} (长度: {len(seq)}):")
        
        # 提取特征
        all_features = extractor.extract_features(seq)
        esm_features = all_features['esm2']['residue_embeddings'].unsqueeze(0).to(device)
        msa_features = all_features['msa']
        
        # 运行模型
        results = model(esm_features, msa_features)
        
        # 分析注意力模式
        if "attention_weights" in results:
            analysis = utils.analyze_attention_patterns(results["attention_weights"], seq)
            all_analyses.append(analysis)
            
            print(f"     ESM→MSA集中度: {analysis['esm_to_msa_concentration']:.4f}")
            print(f"     MSA→ESM集中度: {analysis['msa_to_esm_concentration']:.4f}")
            print(f"     ESM→MSA熵: {analysis['esm_to_msa_entropy']:.4f}")
            print(f"     MSA→ESM熵: {analysis['msa_to_esm_entropy']:.4f}")
    
    # 比较分析结果
    if len(all_analyses) >= 2:
        print("\n2. 序列间注意力模式比较:")
        for key in all_analyses[0].keys():
            values = [analysis[key] for analysis in all_analyses]
            print(f"   {key}:")
            for i, val in enumerate(values):
                print(f"     序列{i+1}: {val:.4f}")
            print(f"     差异: {max(values) - min(values):.4f}")

def main():
    """主演示函数"""
    
    print("🧰 Utils.py 完整功能演示")
    print("=" * 60)
    
    try:
        # 基本使用演示
        demo_basic_usage()
        
        # 输入验证演示
        demo_input_validation()
        
        # 注意力分析演示
        demo_attention_analysis()
        
        # 特征相似性演示
        demo_feature_similarity()
        
        # 可视化演示
        demo_visualization()
        
        # 保存加载演示
        demo_save_load()
        
        # 高级分析演示
        demo_advanced_analysis()
        
        print("\n" + "=" * 60)
        print("🎉 Utils.py 演示完成!")
        print("=" * 60)
        
        print("\n📋 生成的文件:")
        print("  📁 visualization_output/")
        print("    - esm_to_msa_attention.png")
        print("    - msa_to_esm_attention.png") 
        print("    - cross_attention_flow.png")
        print("    - feature_evolution.png")
        print("  📁 analysis_output/")
        print("    - attention_analysis.npz")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
