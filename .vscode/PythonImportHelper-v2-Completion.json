[{"label": "torch", "kind": 6, "isExtraImport": true, "importPath": "torch", "description": "torch", "detail": "torch", "documentation": {}}, {"label": "torch.nn", "kind": 6, "isExtraImport": true, "importPath": "torch.nn", "description": "torch.nn", "detail": "torch.nn", "documentation": {}}, {"label": "torch.nn.functional", "kind": 6, "isExtraImport": true, "importPath": "torch.nn.functional", "description": "torch.nn.functional", "detail": "torch.nn.functional", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Dict", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "List", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "<PERSON><PERSON>", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "Optional", "importPath": "typing", "description": "typing", "isExtraImport": true, "detail": "typing", "documentation": {}}, {"label": "math", "kind": 6, "isExtraImport": true, "importPath": "math", "description": "math", "detail": "math", "documentation": {}}, {"label": "logging", "kind": 6, "isExtraImport": true, "importPath": "logging", "description": "logging", "detail": "logging", "documentation": {}}, {"label": "rearrange", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "repeat", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "rearrange", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "repeat", "importPath": "einops", "description": "einops", "isExtraImport": true, "detail": "einops", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "importPath": "feature_extractor", "description": "feature_extractor", "isExtraImport": true, "detail": "feature_extractor", "documentation": {}}, {"label": "EvoformerModel", "importPath": "evoformer_model", "description": "evoformer_model", "isExtraImport": true, "detail": "evoformer_model", "documentation": {}}, {"label": "visualize_pair_representation", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_contact_map", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_msa_representation", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "plot_representation_evolution", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "print_evoformer_summary", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "analyze_evoformer_outputs", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_attention_weights", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_cross_attention_flow", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "analyze_attention_patterns", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "print_attention_summary", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "validate_attention_fusion_input", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "visualize_gate_values", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "analyze_feature_contributions", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "print_model_summary", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "validate_sequence", "importPath": "utils", "description": "utils", "isExtraImport": true, "detail": "utils", "documentation": {}}, {"label": "numpy", "kind": 6, "isExtraImport": true, "importPath": "numpy", "description": "numpy", "detail": "numpy", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmModel", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "EsmTokenizer", "importPath": "transformers", "description": "transformers", "isExtraImport": true, "detail": "transformers", "documentation": {}}, {"label": "subprocess", "kind": 6, "isExtraImport": true, "importPath": "subprocess", "description": "subprocess", "detail": "subprocess", "documentation": {}}, {"label": "tempfile", "kind": 6, "isExtraImport": true, "importPath": "tempfile", "description": "tempfile", "detail": "tempfile", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "SeqIO", "importPath": "Bio", "description": "Bio", "isExtraImport": true, "detail": "Bio", "documentation": {}}, {"label": "matplotlib.pyplot", "kind": 6, "isExtraImport": true, "importPath": "matplotlib.pyplot", "description": "matplotlib.pyplot", "detail": "matplotlib.pyplot", "documentation": {}}, {"label": "seaborn", "kind": 6, "isExtraImport": true, "importPath": "seaborn", "description": "seaborn", "detail": "seaborn", "documentation": {}}, {"label": "AttentionFusionModel", "importPath": "attention_fusion_model", "description": "attention_fusion_model", "isExtraImport": true, "detail": "attention_fusion_model", "documentation": {}}, {"label": "SeqRecord", "importPath": "Bio.SeqRecord", "description": "Bio.SeqRecord", "isExtraImport": true, "detail": "Bio.SeqRecord", "documentation": {}}, {"label": "SeqRecord", "importPath": "Bio.SeqRecord", "description": "Bio.SeqRecord", "isExtraImport": true, "detail": "Bio.SeqRecord", "documentation": {}}, {"label": "Seq", "importPath": "Bio.Seq", "description": "Bio.Seq", "isExtraImport": true, "detail": "Bio.Seq", "documentation": {}}, {"label": "Seq", "importPath": "Bio.Seq", "description": "Bio.Seq", "isExtraImport": true, "detail": "Bio.Seq", "documentation": {}}, {"label": "GatedFusionModel", "importPath": "gated_fusion_model", "description": "gated_fusion_model", "isExtraImport": true, "detail": "gated_fusion_model", "documentation": {}}, {"label": "OuterProductMean", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class OuterProductMean(nn.Module):\n    \"\"\"外积均值模块，用于从MSA更新Pair representation\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, hidden_dim: int = 32):\n        \"\"\"\n        初始化外积均值模块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度\n        \"\"\"", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "TriangleMultiplication", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class TriangleMultiplication(nn.Module):\n    \"\"\"三角乘法模块，用于Pair representation的自更新\"\"\"\n    def __init__(self, pair_dim: int, hidden_dim: int = 128):\n        \"\"\"\n        初始化三角乘法模块\n        Args:\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度\n        \"\"\"\n        super().__init__()", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "TriangleAttention", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class TriangleAttention(nn.Module):\n    \"\"\"三角注意力模块\"\"\"\n    def __init__(self, pair_dim: int, num_heads: int = 4, dropout: float = 0.1):\n        \"\"\"\n        初始化三角注意力模块\n        Args:\n            pair_dim: Pair特征维度\n            num_heads: 注意力头数\n            dropout: Dropout率\n        \"\"\"", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "MSARowAttention", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class MSARowAttention(nn.Module):\n    \"\"\"MSA行注意力模块\"\"\"\n    def __init__(self, msa_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化MSA行注意力模块\n        Args:\n            msa_dim: MSA特征维度\n            num_heads: 注意力头数\n            dropout: Dropout率\n        \"\"\"", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "EvoformerBlock", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class EvoformerBlock(nn.Module):\n    \"\"\"Evoformer块，包含MSA和Pair的更新\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, single_dim: int,\n                 num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化Evoformer块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            single_dim: Single特征维度", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "EvoformerModel", "kind": 6, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "class EvoformerModel(nn.Module):\n    \"\"\"Evoformer模型主类，实现架构级融合\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, pair_dim: int = 128,\n                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化Evoformer模型\n        Args:\n            esm_dim: ESM-2特征维度 (用作single representation)\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "architectural_fusion.evoformer_model", "description": "architectural_fusion.evoformer_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass OuterProductMean(nn.Module):\n    \"\"\"外积均值模块，用于从MSA更新Pair representation\"\"\"\n    def __init__(self, msa_dim: int, pair_dim: int, hidden_dim: int = 32):\n        \"\"\"\n        初始化外积均值模块\n        Args:\n            msa_dim: MSA特征维度\n            pair_dim: Pair特征维度\n            hidden_dim: 隐藏层维度", "detail": "architectural_fusion.evoformer_model", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "architectural_fusion.example", "description": "architectural_fusion.example", "peekOfCode": "def main():\n    \"\"\"主函数，演示架构级融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"架构级融合蛋白质特征提取模型示例 (Evoformer风格)\")\n    print(\"=\" * 80)", "detail": "architectural_fusion.example", "documentation": {}}, {"label": "demonstrate_evoformer_components", "kind": 2, "importPath": "architectural_fusion.example", "description": "architectural_fusion.example", "peekOfCode": "def demonstrate_evoformer_components(model):\n    \"\"\"演示Evoformer组件的工作原理\"\"\"\n    print(\"\\n1. Evoformer组件测试...\")\n    # 创建测试数据\n    batch_size, seq_len, num_sequences = 1, 50, 32\n    esm_dim, msa_dim, pair_dim = 1280, 256, 128\n    # 模拟输入数据\n    single_repr = torch.randn(batch_size, seq_len, esm_dim)\n    msa_repr = torch.randn(batch_size, num_sequences, seq_len, 21)  # 21 = 20 AA + gap\n    pair_repr = torch.randn(batch_size, seq_len, seq_len, 128)  # 初始pair representation", "detail": "architectural_fusion.example", "documentation": {}}, {"label": "compare_architectural_fusion", "kind": 2, "importPath": "architectural_fusion.example", "description": "architectural_fusion.example", "peekOfCode": "def compare_architectural_fusion():\n    \"\"\"比较架构级融合与其他方法的优势\"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"架构级融合方法比较\")\n    print(\"=\"*60)\n    print(\"Evoformer架构级融合的优势:\")\n    print(\"  ✓ 双流网络设计，充分利用不同特征的特性\")\n    print(\"  ✓ 多层信息交换，实现深度特征融合\")\n    print(\"  ✓ 三角注意力和三角乘法，捕获复杂的残基对关系\")\n    print(\"  ✓ 外积均值操作，有效整合MSA信息到Pair表示\")", "detail": "architectural_fusion.example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "architectural_fusion.example", "description": "architectural_fusion.example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数，演示架构级融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"架构级融合蛋白质特征提取模型示例 (Evoformer风格)\")", "detail": "architectural_fusion.example", "documentation": {}}, {"label": "EvoformerFeatureExtractor", "kind": 6, "importPath": "architectural_fusion.feature_extractor", "description": "architectural_fusion.feature_extractor", "peekOfCode": "class EvoformerFeatureExtractor:\n    \"\"\"Evoformer风格的特征提取器\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")", "detail": "architectural_fusion.feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "architectural_fusion.feature_extractor", "description": "architectural_fusion.feature_extractor", "peekOfCode": "class ProteinFeatureExtractor(EvoformerFeatureExtractor):\n    \"\"\"蛋白质特征提取器的别名，保持接口一致性\"\"\"\n    pass", "detail": "architectural_fusion.feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "architectural_fusion.feature_extractor", "description": "architectural_fusion.feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass EvoformerFeatureExtractor:\n    \"\"\"Evoformer风格的特征提取器\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name", "detail": "architectural_fusion.feature_extractor", "documentation": {}}, {"label": "visualize_pair_representation", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def visualize_pair_representation(pair_repr: torch.Tensor, sequence: str,\n                                 title: str = \"Pair Representation\", save_path: Optional[str] = None,\n                                 figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化Pair representation\n    Args:\n        pair_repr: Pair表示 (seq_len, seq_len, pair_dim) 或 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题\n        save_path: 保存路径", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "visualize_contact_map", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def visualize_contact_map(contact_probs: torch.Tensor, sequence: str,\n                         threshold: float = 0.5, save_path: Optional[str] = None):\n    \"\"\"\n    可视化接触图\n    Args:\n        contact_probs: 接触概率 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        threshold: 接触阈值\n        save_path: 保存路径\n    \"\"\"", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "visualize_msa_representation", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def visualize_msa_representation(msa_repr: torch.Tensor, sequence: str,\n                                max_sequences: int = 50, save_path: Optional[str] = None):\n    \"\"\"\n    可视化MSA representation\n    Args:\n        msa_repr: MSA表示 (num_sequences, seq_len, msa_dim)\n        sequence: 蛋白质序列\n        max_sequences: 最大显示序列数\n        save_path: 保存路径\n    \"\"\"", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "plot_representation_evolution", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def plot_representation_evolution(evolution_data: List[torch.Tensor], \n                                 representation_type: str = \"msa\",\n                                 save_path: Optional[str] = None):\n    \"\"\"\n    绘制表示在不同层的演化\n    Args:\n        evolution_data: 不同层的表示列表\n        representation_type: 表示类型 (\"msa\", \"pair\", \"single\")\n        save_path: 保存路径\n    \"\"\"", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "analyze_evoformer_outputs", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def analyze_evoformer_outputs(results: Dict[str, torch.Tensor]) -> Dict[str, float]:\n    \"\"\"\n    分析Evoformer输出\n    Args:\n        results: 模型输出结果\n    Returns:\n        分析结果\n    \"\"\"\n    analysis = {}\n    # 分析MSA表示", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "save_evoformer_results", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def save_evoformer_results(results: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存Evoformer结果\n    Args:\n        results: 模型输出结果\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    results_np = {}\n    for key, value in results.items():", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "load_evoformer_results", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def load_evoformer_results(load_path: str) -> Dict:\n    \"\"\"\n    加载Evoformer结果\n    Args:\n        load_path: 文件路径\n    Returns:\n        结果字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    results = {}", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "print_evoformer_summary", "kind": 2, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "def print_evoformer_summary(results: Dict[str, torch.Tensor]):\n    \"\"\"\n    打印Evoformer分析摘要\n    Args:\n        results: 模型输出结果\n    \"\"\"\n    print(\"=\" * 60)\n    print(\"EVOFORMER ARCHITECTURAL FUSION SUMMARY\")\n    print(\"=\" * 60)\n    if \"sequence\" in results:", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "architectural_fusion.utils", "description": "architectural_fusion.utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_pair_representation(pair_repr: torch.Tensor, sequence: str,\n                                 title: str = \"Pair Representation\", save_path: Optional[str] = None,\n                                 figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化Pair representation\n    Args:\n        pair_repr: Pair表示 (seq_len, seq_len, pair_dim) 或 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题", "detail": "architectural_fusion.utils", "documentation": {}}, {"label": "MultiHeadCrossAttention", "kind": 6, "importPath": "attention_fusion.attention_fusion_model", "description": "attention_fusion.attention_fusion_model", "peekOfCode": "class MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度\n            value_dim: 值特征维度", "detail": "attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "CrossAttentionBlock", "kind": 6, "importPath": "attention_fusion.attention_fusion_model", "description": "attention_fusion.attention_fusion_model", "peekOfCode": "class CrossAttentionBlock(nn.Module):\n    \"\"\"交叉注意力块\"\"\"\n    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int, \n                 num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化交叉注意力块\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "MSAFeatureProcessor", "kind": 6, "importPath": "attention_fusion.attention_fusion_model", "description": "attention_fusion.attention_fusion_model", "peekOfCode": "class MSAFeatureProcessor(nn.Module):\n    \"\"\"MSA特征处理器\"\"\"\n    def __init__(self, output_dim: int = 256):\n        \"\"\"\n        初始化MSA特征处理器\n        Args:\n            output_dim: 输出特征维度\n        \"\"\"\n        super().__init__()\n        self.output_dim = output_dim", "detail": "attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "AttentionFusionModel", "kind": 6, "importPath": "attention_fusion.attention_fusion_model", "description": "attention_fusion.attention_fusion_model", "peekOfCode": "class AttentionFusionModel(nn.Module):\n    \"\"\"注意力融合模型主类\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512,\n                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化注意力融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "attention_fusion.attention_fusion_model", "description": "attention_fusion.attention_fusion_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MultiHeadCrossAttention(nn.Module):\n    \"\"\"多头交叉注意力模块\"\"\"\n    def __init__(self, query_dim: int, key_dim: int, value_dim: int, \n                 hidden_dim: int, num_heads: int = 8, dropout: float = 0.1):\n        \"\"\"\n        初始化多头交叉注意力\n        Args:\n            query_dim: 查询特征维度\n            key_dim: 键特征维度", "detail": "attention_fusion.attention_fusion_model", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "attention_fusion.example", "description": "attention_fusion.example", "peekOfCode": "def main():\n    \"\"\"主函数，演示注意力融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"注意力融合蛋白质特征提取模型示例\")\n    print(\"=\" * 80)", "detail": "attention_fusion.example", "documentation": {}}, {"label": "demonstrate_attention_mechanisms", "kind": 2, "importPath": "attention_fusion.example", "description": "attention_fusion.example", "peekOfCode": "def demonstrate_attention_mechanisms(model):\n    \"\"\"演示注意力机制的工作原理\"\"\"\n    print(\"\\n1. 注意力机制测试...\")\n    # 创建测试数据\n    batch_size, seq_len = 1, 50\n    esm_dim, msa_dim = 1280, 256\n    # 模拟输入数据\n    esm_features = torch.randn(batch_size, seq_len, esm_dim)\n    msa_features = {\n        \"query_representation\": torch.randn(seq_len, msa_dim),", "detail": "attention_fusion.example", "documentation": {}}, {"label": "compare_fusion_methods", "kind": 2, "importPath": "attention_fusion.example", "description": "attention_fusion.example", "peekOfCode": "def compare_fusion_methods():\n    \"\"\"比较不同融合方法的效果\"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"融合方法比较\")\n    print(\"=\"*60)\n    # 这里可以添加与门控融合和架构级融合的比较代码\n    # 由于篇幅限制，这里只是一个框架\n    print(\"注意力融合的优势:\")\n    print(\"  ✓ 能够捕获复杂的特征间关系\")\n    print(\"  ✓ 提供可解释的注意力权重\")", "detail": "attention_fusion.example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "attention_fusion.example", "description": "attention_fusion.example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数，演示注意力融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"注意力融合蛋白质特征提取模型示例\")", "detail": "attention_fusion.example", "documentation": {}}, {"label": "MSATransformerFeatureExtractor", "kind": 6, "importPath": "attention_fusion.feature_extractor", "description": "attention_fusion.feature_extractor", "peekOfCode": "class MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化MSA Transformer特征提取器\n        Args:\n            model_name: MSA Transformer模型名称\n        \"\"\"\n        self.model_name = model_name\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")", "detail": "attention_fusion.feature_extractor", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "attention_fusion.feature_extractor", "description": "attention_fusion.feature_extractor", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器，支持ESM-2和MSA特征提取\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\",\n                 msa_model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n            msa_model_name: MSA Transformer模型名称\n        \"\"\"", "detail": "attention_fusion.feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "attention_fusion.feature_extractor", "description": "attention_fusion.feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass MSATransformerFeatureExtractor:\n    \"\"\"MSA Transformer特征提取器\"\"\"\n    def __init__(self, model_name: str = \"facebook/esm_msa1b_t12_100M_UR50S\"):\n        \"\"\"\n        初始化MSA Transformer特征提取器\n        Args:\n            model_name: MSA Transformer模型名称\n        \"\"\"\n        self.model_name = model_name", "detail": "attention_fusion.feature_extractor", "documentation": {}}, {"label": "visualize_attention_weights", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题\n        save_path: 保存路径", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "visualize_cross_attention_flow", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def visualize_cross_attention_flow(esm_to_msa_weights: torch.Tensor, \n                                  msa_to_esm_weights: torch.Tensor,\n                                  sequence: str, save_path: Optional[str] = None):\n    \"\"\"\n    可视化交叉注意力流\n    Args:\n        esm_to_msa_weights: ESM-2到MSA的注意力权重\n        msa_to_esm_weights: MSA到ESM-2的注意力权重\n        sequence: 蛋白质序列\n        save_path: 保存路径", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "analyze_attention_patterns", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def analyze_attention_patterns(attention_weights: List[Dict], sequence: str) -> Dict[str, float]:\n    \"\"\"\n    分析注意力模式\n    Args:\n        attention_weights: 注意力权重列表\n        sequence: 蛋白质序列\n    Returns:\n        注意力模式分析结果\n    \"\"\"\n    # 计算平均注意力权重", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "compute_feature_similarity", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def compute_feature_similarity(features1: torch.Tensor, features2: torch.Tensor, \n                             method: str = \"cosine\") -> Dict[str, float]:\n    \"\"\"\n    计算特征相似性\n    Args:\n        features1: 第一组特征\n        features2: 第二组特征\n        method: 相似性计算方法\n    Returns:\n        相似性分析结果", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "plot_feature_evolution", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def plot_feature_evolution(features_by_layer: List[torch.Tensor], sequence: str,\n                          save_path: Optional[str] = None):\n    \"\"\"\n    绘制特征在不同层的演化\n    Args:\n        features_by_layer: 不同层的特征列表\n        sequence: 蛋白质序列\n        save_path: 保存路径\n    \"\"\"\n    num_layers = len(features_by_layer)", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "save_attention_analysis", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def save_attention_analysis(results: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存注意力分析结果\n    Args:\n        results: 分析结果\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    results_np = {}\n    for key, value in results.items():", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "load_attention_analysis", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def load_attention_analysis(load_path: str) -> Dict:\n    \"\"\"\n    加载注意力分析结果\n    Args:\n        load_path: 文件路径\n    Returns:\n        分析结果字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    results = {}", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "print_attention_summary", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def print_attention_summary(results: Dict[str, torch.Tensor]):\n    \"\"\"\n    打印注意力分析摘要\n    Args:\n        results: 模型输出结果\n    \"\"\"\n    print(\"=\" * 60)\n    print(\"ATTENTION FUSION ANALYSIS SUMMARY\")\n    print(\"=\" * 60)\n    if \"attention_weights\" in results:", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "validate_attention_fusion_input", "kind": 2, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "def validate_attention_fusion_input(esm_features: torch.Tensor, msa_features: Dict) -> bool:\n    \"\"\"\n    验证注意力融合模型的输入\n    Args:\n        esm_features: ESM-2特征\n        msa_features: MSA特征字典\n    Returns:\n        是否为有效输入\n    \"\"\"\n    try:", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "attention_fusion.utils", "description": "attention_fusion.utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_attention_weights(attention_weights: torch.Tensor, sequence: str,\n                              title: str = \"Attention Weights\", save_path: Optional[str] = None,\n                              figsize: Tuple[int, int] = (10, 8)):\n    \"\"\"\n    可视化注意力权重矩阵\n    Args:\n        attention_weights: 注意力权重 (seq_len, seq_len)\n        sequence: 蛋白质序列\n        title: 图像标题", "detail": "attention_fusion.utils", "documentation": {}}, {"label": "main", "kind": 2, "importPath": "gated_fusion.example", "description": "gated_fusion.example", "peekOfCode": "def main():\n    \"\"\"主函数，演示门控融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"门控融合蛋白质特征提取模型示例\")\n    print(\"=\" * 80)", "detail": "gated_fusion.example", "documentation": {}}, {"label": "demonstrate_model_features", "kind": 2, "importPath": "gated_fusion.example", "description": "gated_fusion.example", "peekOfCode": "def demonstrate_model_features():\n    \"\"\"演示模型的高级功能\"\"\"\n    print(\"\\n\" + \"=\"*60)\n    print(\"高级功能演示\")\n    print(\"=\"*60)\n    # 创建简单的测试数据\n    batch_size, seq_len, esm_dim, msa_dim = 2, 50, 1280, 256\n    # 模拟输入数据\n    esm_features = torch.randn(batch_size, seq_len, esm_dim)\n    msa_features = {", "detail": "gated_fusion.example", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "gated_fusion.example", "description": "gated_fusion.example", "peekOfCode": "logger = logging.getLogger(__name__)\ndef main():\n    \"\"\"主函数，演示门控融合模型的使用\"\"\"\n    # 示例蛋白质序列\n    test_sequences = [\n        \"MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG\",\n        \"MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL\"\n    ]\n    print(\"=\" * 80)\n    print(\"门控融合蛋白质特征提取模型示例\")", "detail": "gated_fusion.example", "documentation": {}}, {"label": "ProteinFeatureExtractor", "kind": 6, "importPath": "gated_fusion.feature_extractor", "description": "gated_fusion.feature_extractor", "peekOfCode": "class ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器，支持ESM-2和MSA特征提取\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name\n        self.device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")", "detail": "gated_fusion.feature_extractor", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "gated_fusion.feature_extractor", "description": "gated_fusion.feature_extractor", "peekOfCode": "logger = logging.getLogger(__name__)\nclass ProteinFeatureExtractor:\n    \"\"\"蛋白质特征提取器，支持ESM-2和MSA特征提取\"\"\"\n    def __init__(self, esm_model_name: str = \"facebook/esm2_t33_650M_UR50D\"):\n        \"\"\"\n        初始化特征提取器\n        Args:\n            esm_model_name: ESM-2模型名称\n        \"\"\"\n        self.esm_model_name = esm_model_name", "detail": "gated_fusion.feature_extractor", "documentation": {}}, {"label": "GatingUnit", "kind": 6, "importPath": "gated_fusion.gated_fusion_model", "description": "gated_fusion.gated_fusion_model", "peekOfCode": "class GatingUnit(nn.Module):\n    \"\"\"门控单元，用于动态权衡不同特征的重要性\"\"\"\n    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int = 256):\n        \"\"\"\n        初始化门控单元\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度\n        \"\"\"", "detail": "gated_fusion.gated_fusion_model", "documentation": {}}, {"label": "MSAFeatureProcessor", "kind": 6, "importPath": "gated_fusion.gated_fusion_model", "description": "gated_fusion.gated_fusion_model", "peekOfCode": "class MSAFeatureProcessor(nn.Module):\n    \"\"\"MSA特征处理器，将多种MSA特征融合成统一表示\"\"\"\n    def __init__(self, output_dim: int = 256):\n        \"\"\"\n        初始化MSA特征处理器\n        Args:\n            output_dim: 输出特征维度\n        \"\"\"\n        super().__init__()\n        self.output_dim = output_dim", "detail": "gated_fusion.gated_fusion_model", "documentation": {}}, {"label": "GatedFusionModel", "kind": 6, "importPath": "gated_fusion.gated_fusion_model", "description": "gated_fusion.gated_fusion_model", "peekOfCode": "class GatedFusionModel(nn.Module):\n    \"\"\"门控融合模型主类\"\"\"\n    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512, \n                 num_layers: int = 2, dropout: float = 0.1):\n        \"\"\"\n        初始化门控融合模型\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "gated_fusion.gated_fusion_model", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "gated_fusion.gated_fusion_model", "description": "gated_fusion.gated_fusion_model", "peekOfCode": "logger = logging.getLogger(__name__)\nclass GatingUnit(nn.Module):\n    \"\"\"门控单元，用于动态权衡不同特征的重要性\"\"\"\n    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int = 256):\n        \"\"\"\n        初始化门控单元\n        Args:\n            esm_dim: ESM-2特征维度\n            msa_dim: MSA特征维度\n            hidden_dim: 隐藏层维度", "detail": "gated_fusion.gated_fusion_model", "documentation": {}}, {"label": "visualize_gate_values", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def visualize_gate_values(gate_values: torch.Tensor, sequence: str, \n                         save_path: Optional[str] = None, figsize: Tuple[int, int] = (12, 6)):\n    \"\"\"\n    可视化门控值\n    Args:\n        gate_values: 门控值 (seq_len,)\n        sequence: 蛋白质序列\n        save_path: 保存路径\n        figsize: 图像大小\n    \"\"\"", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "analyze_feature_contributions", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def analyze_feature_contributions(results: Dict[str, torch.Tensor]) -> Dict[str, float]:\n    \"\"\"\n    分析特征贡献度\n    Args:\n        results: 模型输出结果\n    Returns:\n        特征贡献度分析结果\n    \"\"\"\n    gate_values = results[\"gate_values\"]\n    if isinstance(gate_values, torch.Tensor):", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "compare_features", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def compare_features(esm2_features: torch.Tensor, msa_features: torch.Tensor, \n                    fused_features: torch.Tensor, method: str = \"cosine\") -> Dict[str, float]:\n    \"\"\"\n    比较不同特征之间的相似性\n    Args:\n        esm2_features: ESM-2特征\n        msa_features: MSA特征\n        fused_features: 融合后特征\n        method: 相似性计算方法\n    Returns:", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "save_features", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def save_features(features: Dict[str, torch.Tensor], save_path: str):\n    \"\"\"\n    保存特征到文件\n    Args:\n        features: 特征字典\n        save_path: 保存路径\n    \"\"\"\n    # 转换为numpy格式保存\n    features_np = {}\n    for key, value in features.items():", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "load_features", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def load_features(load_path: str) -> Dict[str, torch.Tensor]:\n    \"\"\"\n    从文件加载特征\n    Args:\n        load_path: 文件路径\n    Returns:\n        特征字典\n    \"\"\"\n    data = np.load(load_path, allow_pickle=True)\n    features = {}", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "print_model_summary", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def print_model_summary(model, input_shapes: Dict[str, Tuple]):\n    \"\"\"\n    打印模型摘要\n    Args:\n        model: 模型实例\n        input_shapes: 输入形状字典\n    \"\"\"\n    total_params = sum(p.numel() for p in model.parameters())\n    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)\n    print(\"=\" * 60)", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "validate_sequence", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def validate_sequence(sequence: str) -> bool:\n    \"\"\"\n    验证蛋白质序列格式\n    Args:\n        sequence: 蛋白质序列\n    Returns:\n        是否为有效序列\n    \"\"\"\n    valid_aa = set(\"ACDEFGHIKLMNPQRSTVWY\")\n    sequence = sequence.upper().strip()", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "batch_process_sequences", "kind": 2, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "def batch_process_sequences(sequences: List[str], model, feature_extractor, \n                          batch_size: int = 8, msa_database_path: str = None) -> List[Dict]:\n    \"\"\"\n    批量处理蛋白质序列\n    Args:\n        sequences: 蛋白质序列列表\n        model: 融合模型\n        feature_extractor: 特征提取器\n        batch_size: 批次大小\n        msa_database_path: MSA数据库路径", "detail": "gated_fusion.utils", "documentation": {}}, {"label": "logger", "kind": 5, "importPath": "gated_fusion.utils", "description": "gated_fusion.utils", "peekOfCode": "logger = logging.getLogger(__name__)\ndef visualize_gate_values(gate_values: torch.Tensor, sequence: str, \n                         save_path: Optional[str] = None, figsize: Tuple[int, int] = (12, 6)):\n    \"\"\"\n    可视化门控值\n    Args:\n        gate_values: 门控值 (seq_len,)\n        sequence: 蛋白质序列\n        save_path: 保存路径\n        figsize: 图像大小", "detail": "gated_fusion.utils", "documentation": {}}]