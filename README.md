# 蛋白质特征融合方法集合

基于ESM-2和MSA Transformer的三种蛋白质特征融合方法实现，包括门控融合、注意力融合和架构级融合。

## 项目概述

本项目实现了三种不同复杂度和应用场景的蛋白质特征融合方法：

1. **门控融合 (Gated Fusion)** - 简单高效的动态权衡方法
2. **注意力融合 (Attention Fusion)** - 基于交叉注意力的精细化融合
3. **架构级融合 (Architectural Fusion)** - Evoformer风格的双流网络架构

## 项目结构

```
.
├── README.md                    # 总体说明文档
├── feature_fusion.md           # 特征融合方案详细说明
├── gated_fusion/               # 门控融合方法
│   ├── README.md
│   ├── requirements.txt
│   ├── feature_extractor.py
│   ├── gated_fusion_model.py
│   ├── utils.py
│   └── example.py
├── attention_fusion/           # 注意力融合方法
│   ├── README.md
│   ├── requirements.txt
│   ├── feature_extractor.py
│   ├── attention_fusion_model.py
│   ├── utils.py
│   └── example.py
└── architectural_fusion/       # 架构级融合方法
    ├── README.md
    ├── requirements.txt
    ├── feature_extractor.py
    ├── evoformer_model.py
    ├── utils.py
    └── example.py
```

## 方法比较

| 特性 | 门控融合 | 注意力融合 | 架构级融合 |
|------|----------|------------|------------|
| **复杂度** | 低 | 中等 | 高 |
| **参数量** | ~10M | ~50M | ~200M+ |
| **计算需求** | 低 | 中等 | 高 |
| **内存需求** | 8GB | 16GB | 32GB+ |
| **训练时间** | 快 | 中等 | 慢 |
| **可解释性** | 高 (门控值) | 中等 (注意力权重) | 低 |
| **适用场景** | 快速原型、资源受限 | 通用特征融合 | 结构预测、高精度任务 |

## 快速开始

### 1. 环境准备

```bash
# 基础环境
conda create -n protein_fusion python=3.8
conda activate protein_fusion

# 安装PyTorch (根据您的CUDA版本)
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装其他依赖
pip install transformers fair-esm biopython numpy scipy matplotlib seaborn tqdm einops
```

### 2. 选择合适的方法

#### 门控融合 - 适合快速实验

```bash
cd gated_fusion
pip install -r requirements.txt
python example.py
```

#### 注意力融合 - 平衡性能和复杂度

```bash
cd attention_fusion
pip install -r requirements.txt
python example.py
```

#### 架构级融合 - 追求最高性能

```bash
cd architectural_fusion
pip install -r requirements.txt
python example.py
```

### 3. 基本使用示例

```python
# 以门控融合为例
from gated_fusion.feature_extractor import ProteinFeatureExtractor
from gated_fusion.gated_fusion_model import GatedFusionModel

# 初始化
extractor = ProteinFeatureExtractor()
model = GatedFusionModel(esm_dim=1280, msa_dim=256, hidden_dim=512)

# 处理蛋白质序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
results = model.extract_and_fuse(sequence, extractor)

# 获取融合特征
fused_features = results["fused_features"]
print(f"融合特征形状: {fused_features.shape}")
```

## 核心特性

### ESM-2特征提取
- 支持多种ESM-2模型 (35M到15B参数)
- 残基级别和序列级别嵌入
- 注意力图提取
- GPU/CPU自适应

### MSA特征提取
- HHblits自动MSA生成
- MSA Transformer特征提取
- 传统统计特征 (PSSM, 保守性, 共进化)
- 容错处理 (单序列备选)

### 融合策略
- **门控融合**: 动态权衡ESM-2和MSA特征
- **注意力融合**: 交叉注意力机制实现精细化融合
- **架构级融合**: 双流网络多层信息交换

## 应用场景

### 1. 蛋白质功能预测
```python
# 使用融合特征进行功能分类
fused_features = model.extract_and_fuse(sequence, extractor)["fused_features"]
# 接下游分类器...
```

### 2. 蛋白质结构预测
```python
# 使用架构级融合的Pair表示
results = evoformer_model.extract_and_fuse(sequence, extractor)
contact_probs = evoformer_model.get_structure_predictions(results["pair_representation"])
```

### 3. 突变效应预测
```python
# 比较野生型和突变型的特征差异
wt_features = model.extract_and_fuse(wild_type_seq, extractor)
mut_features = model.extract_and_fuse(mutant_seq, extractor)
# 计算特征差异...
```

## 性能基准

在标准测试集上的性能比较 (示例数据):

| 方法 | 功能预测准确率 | 结构预测精度 | 推理速度 (seq/min) |
|------|----------------|--------------|-------------------|
| 门控融合 | 85.2% | - | 120 |
| 注意力融合 | 87.8% | 0.72 (GDT-TS) | 45 |
| 架构级融合 | 89.1% | 0.85 (GDT-TS) | 12 |

## 安装要求

### 最小配置
- Python 3.8+
- 8GB RAM
- 4GB GPU内存 (可选)

### 推荐配置
- Python 3.9+
- 32GB RAM
- 16GB+ GPU内存 (RTX 3090, V100等)
- SSD存储

### 依赖包
- torch >= 1.9.0
- transformers >= 4.20.0
- fair-esm >= 2.0.0
- biopython >= 1.79
- numpy, scipy, matplotlib, seaborn
- einops (注意力融合和架构级融合)

## 故障排除

### 常见问题

**Q: 内存不足错误**
A: 
- 使用较小的ESM-2模型
- 减少批次大小
- 限制MSA序列数量

**Q: HHblits未找到**
A:
- 安装HHsuite: `conda install -c bioconda hhsuite`
- 或者模型会自动使用单序列MSA

**Q: GPU内存不足**
A:
- 设置 `CUDA_VISIBLE_DEVICES=""` 使用CPU
- 使用混合精度训练
- 减少模型参数

### 性能优化建议

1. **使用GPU加速**: 确保CUDA正确安装
2. **批量处理**: 使用提供的批量处理函数
3. **模型选择**: 根据任务需求选择合适的复杂度
4. **内存管理**: 及时清理不需要的中间结果

## 引用

如果您在研究中使用了本项目的代码，请引用：

```bibtex
@software{protein_fusion_methods,
  title={Protein Feature Fusion Methods: ESM-2 and MSA Integration},
  author={Your Name},
  year={2024},
  url={https://github.com/your-repo/protein-fusion}
}
```

相关论文引用：
```bibtex
@article{lin2023evolutionary,
  title={Evolutionary-scale prediction of atomic-level protein structure with a language model},
  author={Lin, Zeming and others},
  journal={Science},
  year={2023}
}

@article{jumper2021highly,
  title={Highly accurate protein structure prediction with AlphaFold},
  author={Jumper, John and others},
  journal={Nature},
  year={2021}
}
```

## 许可证

本项目采用MIT许可证。详见各子目录中的LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request！

## 联系方式

如有问题或建议，请联系：
- Email: [<EMAIL>]
- GitHub Issues: [项目Issues页面]

---

**注意**: 本项目仅用于研究目的。在生产环境中使用前请充分测试。
