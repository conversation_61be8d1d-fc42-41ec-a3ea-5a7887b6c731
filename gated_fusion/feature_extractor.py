"""
蛋白质特征提取器
支持ESM-2和MSA特征提取
"""

import torch
import torch.nn as nn
import numpy as np
from transformers import EsmModel, EsmTokenizer
import subprocess
import tempfile
import os
from typing import Dict, List, Tuple, Optional
from Bio import SeqIO
from Bio.SeqRecord import SeqRecord
from Bio.Seq import Seq
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ProteinFeatureExtractor:
    """蛋白质特征提取器，支持ESM-2和MSA特征提取"""
    
    def __init__(self, esm_model_name: str = "facebook/esm2_t33_650M_UR50D"):
        """
        初始化特征提取器
        
        Args:
            esm_model_name: ESM-2模型名称
        """
        self.esm_model_name = esm_model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载ESM-2模型和tokenizer
        logger.info(f"Loading ESM-2 model: {esm_model_name}")
        self.esm_tokenizer = EsmTokenizer.from_pretrained(esm_model_name)
        self.esm_model = EsmModel.from_pretrained(esm_model_name)
        self.esm_model.to(self.device)
        self.esm_model.eval()
        
        # ESM-2特征维度
        self.esm_dim = self.esm_model.config.hidden_size
        
    def extract_esm2_features(self, sequence: str) -> Dict[str, torch.Tensor]:
        """
        提取ESM-2特征
        
        Args:
            sequence: 蛋白质序列
            
        Returns:
            包含不同类型ESM-2特征的字典
        """
        # Tokenize序列
        inputs = self.esm_tokenizer(sequence, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.esm_model(**inputs, output_attentions=True, output_hidden_states=True)
        
        # 提取不同类型的特征
        features = {
            # 残基级别嵌入 (去除CLS和SEP token)
            "residue_embeddings": outputs.last_hidden_state[0, 1:-1, :],  # (L, D)
            # 序列级别嵌入 (CLS token)
            "sequence_embedding": outputs.last_hidden_state[0, 0, :],  # (D,)
            # 注意力图
            "attention_maps": torch.stack([layer_attention[0] for layer_attention in outputs.attentions]),  # (num_layers, num_heads, L+2, L+2)
        }
        
        # 移除CLS和SEP token的注意力
        features["attention_maps"] = features["attention_maps"][:, :, 1:-1, 1:-1]  # (num_layers, num_heads, L, L)
        
        return features
    
    def generate_msa_with_hhblits(self, sequence: str, database_path: str = None, 
                                  max_iterations: int = 3, e_value: float = 1e-3) -> str:
        """
        使用HHblits生成MSA
        
        Args:
            sequence: 查询序列
            database_path: 数据库路径 (如果为None，将使用默认路径)
            max_iterations: 最大迭代次数
            e_value: E-value阈值
            
        Returns:
            MSA文件路径
        """
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as query_file:
            query_file.write(f">query\n{sequence}\n")
            query_path = query_file.name
        
        msa_path = query_path.replace('.fasta', '.a3m')
        
        try:
            # 构建HHblits命令
            cmd = [
                "hhblits",
                "-i", query_path,
                "-o", msa_path,
                "-n", str(max_iterations),
                "-e", str(e_value)
            ]
            
            if database_path:
                cmd.extend(["-d", database_path])
            
            # 运行HHblits
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                logger.warning(f"HHblits failed: {result.stderr}")
                # 如果HHblits失败，创建只包含查询序列的MSA
                with open(msa_path, 'w') as f:
                    f.write(f">query\n{sequence}\n")
            
            return msa_path
            
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.warning(f"HHblits error: {e}. Creating single-sequence MSA.")
            # 创建只包含查询序列的MSA
            with open(msa_path, 'w') as f:
                f.write(f">query\n{sequence}\n")
            return msa_path
        
        finally:
            # 清理临时文件
            if os.path.exists(query_path):
                os.unlink(query_path)
    
    def extract_msa_features(self, msa_path: str, sequence: str) -> Dict[str, torch.Tensor]:
        """
        从MSA提取特征
        
        Args:
            msa_path: MSA文件路径
            sequence: 查询序列
            
        Returns:
            包含MSA特征的字典
        """
        # 读取MSA
        sequences = []
        try:
            with open(msa_path, 'r') as f:
                for record in SeqIO.parse(f, "fasta"):
                    sequences.append(str(record.seq))
        except:
            # 如果读取失败，使用单序列
            sequences = [sequence]
        
        # 确保查询序列在第一位
        if sequence not in sequences:
            sequences.insert(0, sequence)
        elif sequences[0] != sequence:
            sequences.remove(sequence)
            sequences.insert(0, sequence)
        
        seq_len = len(sequence)
        num_seqs = len(sequences)
        
        # 计算PSSM (Position-Specific Scoring Matrix)
        pssm = self._calculate_pssm(sequences, seq_len)
        
        # 计算序列权重
        seq_weights = self._calculate_sequence_weights(sequences)
        
        # 计算保守性分数
        conservation_scores = self._calculate_conservation(sequences, seq_weights)
        
        # 计算共进化特征 (简化版)
        coevolution_matrix = self._calculate_coevolution(sequences, seq_weights)
        
        features = {
            "pssm": torch.tensor(pssm, dtype=torch.float32),  # (L, 20)
            "conservation_scores": torch.tensor(conservation_scores, dtype=torch.float32),  # (L,)
            "sequence_weights": torch.tensor(seq_weights, dtype=torch.float32),  # (num_seqs,)
            "coevolution_matrix": torch.tensor(coevolution_matrix, dtype=torch.float32),  # (L, L)
            "num_sequences": torch.tensor(num_seqs, dtype=torch.float32),  # scalar
        }
        
        return features
    
    def _calculate_pssm(self, sequences: List[str], seq_len: int) -> np.ndarray:
        """计算PSSM矩阵"""
        aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY")}
        pssm = np.zeros((seq_len, 20))
        
        for pos in range(seq_len):
            aa_counts = np.zeros(20)
            total_count = 0
            
            for seq in sequences:
                if pos < len(seq) and seq[pos] in aa_to_idx:
                    aa_counts[aa_to_idx[seq[pos]]] += 1
                    total_count += 1
            
            if total_count > 0:
                pssm[pos] = aa_counts / total_count
            else:
                pssm[pos] = np.ones(20) / 20  # 均匀分布
        
        return pssm
    
    def _calculate_sequence_weights(self, sequences: List[str]) -> np.ndarray:
        """计算序列权重"""
        num_seqs = len(sequences)
        weights = np.ones(num_seqs) / num_seqs
        return weights
    
    def _calculate_conservation(self, sequences: List[str], weights: np.ndarray) -> np.ndarray:
        """计算保守性分数"""
        seq_len = len(sequences[0])
        conservation = np.zeros(seq_len)
        
        for pos in range(seq_len):
            aa_counts = {}
            for i, seq in enumerate(sequences):
                if pos < len(seq):
                    aa = seq[pos]
                    aa_counts[aa] = aa_counts.get(aa, 0) + weights[i]
            
            # 计算香农熵作为保守性度量
            entropy = 0
            for count in aa_counts.values():
                if count > 0:
                    entropy -= count * np.log2(count)
            
            conservation[pos] = -entropy  # 负熵，值越大越保守
        
        return conservation
    
    def _calculate_coevolution(self, sequences: List[str], weights: np.ndarray) -> np.ndarray:
        """计算简化的共进化矩阵"""
        seq_len = len(sequences[0])
        coev_matrix = np.zeros((seq_len, seq_len))
        
        # 简化的互信息计算
        for i in range(seq_len):
            for j in range(i+1, seq_len):
                # 计算位置i和j的氨基酸对频率
                pair_counts = {}
                total_weight = 0
                
                for k, seq in enumerate(sequences):
                    if i < len(seq) and j < len(seq):
                        pair = (seq[i], seq[j])
                        pair_counts[pair] = pair_counts.get(pair, 0) + weights[k]
                        total_weight += weights[k]
                
                # 计算互信息 (简化版)
                mi = 0
                if total_weight > 0:
                    for count in pair_counts.values():
                        if count > 0:
                            prob = count / total_weight
                            mi += prob * np.log2(prob + 1e-8)
                
                coev_matrix[i, j] = coev_matrix[j, i] = -mi
        
        return coev_matrix
    
    def extract_features(self, sequence: str, msa_database_path: str = None) -> Dict[str, torch.Tensor]:
        """
        提取完整的蛋白质特征
        
        Args:
            sequence: 蛋白质序列
            msa_database_path: MSA数据库路径
            
        Returns:
            包含ESM-2和MSA特征的字典
        """
        logger.info("Extracting ESM-2 features...")
        esm_features = self.extract_esm2_features(sequence)
        
        logger.info("Generating MSA and extracting MSA features...")
        msa_path = self.generate_msa_with_hhblits(sequence, msa_database_path)
        msa_features = self.extract_msa_features(msa_path, sequence)
        
        # 清理MSA文件
        if os.path.exists(msa_path):
            os.unlink(msa_path)
        
        # 合并特征
        all_features = {
            "esm2": esm_features,
            "msa": msa_features,
            "sequence": sequence,
            "sequence_length": len(sequence)
        }
        
        return all_features
