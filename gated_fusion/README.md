# 门控融合蛋白质特征提取方法

## 概述

本模块实现了基于ESM-2和MSA特征的门控融合方法，通过门控机制动态地权衡两种特征的重要性，实现自适应的特征融合。

## 特点

- **动态权衡**: 门控机制可以根据输入的具体情况，决定更相信ESM-2的信息还是MSA的信息
- **自适应性**: 对于没有好MSA的蛋白，门控值可能趋向于更多地使用ESM-2特征
- **可解释性**: 门控值提供了特征重要性的直观解释

## 文件结构

```
gated_fusion/
├── README.md                 # 使用说明
├── requirements.txt          # 依赖包
├── feature_extractor.py      # 特征提取器
├── gated_fusion_model.py     # 门控融合模型
├── utils.py                  # 工具函数
└── example.py               # 使用示例
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

```python
from gated_fusion_model import GatedFusionModel
from feature_extractor import ProteinFeatureExtractor

# 初始化特征提取器和模型
extractor = ProteinFeatureExtractor()
model = GatedFusionModel(esm_dim=1280, msa_dim=20, hidden_dim=512)

# 提取特征并融合
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
fused_features = model.extract_and_fuse(sequence, extractor)
```

## 详细使用说明

### 1. 环境配置

确保您的系统满足以下要求：
- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 至少8GB内存

### 2. 安装步骤

```bash
# 克隆或下载代码
cd gated_fusion

# 安装依赖
pip install -r requirements.txt

# 可选：安装HHblits用于MSA生成
# Ubuntu/Debian:
sudo apt-get install hhsuite
# 或从源码编译：https://github.com/soedinglab/hh-suite
```

### 3. 基本使用

#### 3.1 简单示例

```python
from feature_extractor import ProteinFeatureExtractor
from gated_fusion_model import GatedFusionModel

# 初始化
extractor = ProteinFeatureExtractor()
model = GatedFusionModel(esm_dim=1280, msa_dim=256, hidden_dim=512)

# 处理单个序列
sequence = "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG"
results = model.extract_and_fuse(sequence, extractor)

# 获取融合特征
fused_features = results["fused_features"]  # (1, seq_len, hidden_dim)
gate_values = results["gate_values"]        # (1, seq_len)
```

#### 3.2 批量处理

```python
from utils import batch_process_sequences

sequences = ["SEQUENCE1", "SEQUENCE2", "SEQUENCE3"]
results = batch_process_sequences(sequences, model, extractor, batch_size=2)
```

#### 3.3 特征分析

```python
from utils import analyze_feature_contributions, visualize_gate_values

# 分析特征贡献度
analysis = analyze_feature_contributions(results)
print(f"ESM-2平均权重: {analysis['mean_esm2_weight']:.3f}")

# 可视化门控值
visualize_gate_values(results["gate_values"][0], sequence, save_path="gate_analysis.png")
```

### 4. 参数说明

#### 4.1 GatedFusionModel参数

- `esm_dim` (int): ESM-2特征维度，默认1280
- `msa_dim` (int): MSA特征维度，默认256
- `hidden_dim` (int): 隐藏层维度，默认512
- `num_layers` (int): Transformer层数，默认2
- `dropout` (float): Dropout率，默认0.1

#### 4.2 ProteinFeatureExtractor参数

- `esm_model_name` (str): ESM-2模型名称，默认"facebook/esm2_t33_650M_UR50D"

### 5. 输出说明

模型输出包含以下字段：

- `fused_features`: 融合后的特征 (batch_size, seq_len, hidden_dim)
- `gate_values`: 门控值 (batch_size, seq_len)
- `esm_contribution`: ESM-2的平均贡献度
- `msa_contribution`: MSA的平均贡献度
- `sequence`: 输入序列
- `sequence_length`: 序列长度

### 6. 高级功能

#### 6.1 自定义MSA数据库

```python
# 使用自定义MSA数据库
results = model.extract_and_fuse(
    sequence,
    extractor,
    msa_database_path="/path/to/your/database"
)
```

#### 6.2 特征重要性分析

```python
importance = model.get_feature_importance(results["gate_values"])
print(f"ESM-2主导位置: {importance['esm2_dominant_positions']}")
print(f"MSA主导位置: {importance['msa_dominant_positions']}")
```

### 7. 故障排除

#### 7.1 常见问题

**Q: 内存不足错误**
A: 减少batch_size或使用较小的ESM-2模型（如esm2_t12_35M_UR50D）

**Q: HHblits未找到**
A: 确保HHblits已正确安装并在PATH中，或者模型会自动使用单序列MSA

**Q: CUDA内存不足**
A: 设置环境变量 `CUDA_VISIBLE_DEVICES=""` 强制使用CPU

#### 7.2 性能优化

- 使用GPU加速：确保PyTorch支持CUDA
- 批量处理：使用`batch_process_sequences`函数
- 模型量化：可以使用torch.quantization减少内存使用

### 8. 引用

如果您在研究中使用了此代码，请引用相关论文：

```bibtex
@article{your_paper,
  title={Gated Fusion of ESM-2 and MSA Features for Protein Analysis},
  author={Your Name},
  journal={Your Journal},
  year={2024}
}
```

### 9. 许可证

本项目采用MIT许可证。详见LICENSE文件。

### 10. 联系方式

如有问题或建议，请联系：[<EMAIL>]

---

更多详细示例请参考 `example.py` 文件。
