"""
门控融合模型使用示例
"""

import torch
import logging
from feature_extractor import ProteinFeatureExtractor
from gated_fusion_model import GatedFusionModel
from utils import (
    visualize_gate_values, 
    analyze_feature_contributions,
    print_model_summary,
    validate_sequence
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """主函数，演示门控融合模型的使用"""
    
    # 示例蛋白质序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL"
    ]
    
    print("=" * 80)
    print("门控融合蛋白质特征提取模型示例")
    print("=" * 80)
    
    # 1. 初始化特征提取器
    print("\n1. 初始化特征提取器...")
    try:
        extractor = ProteinFeatureExtractor(esm_model_name="facebook/esm2_t33_650M_UR50D")
        print("✓ 特征提取器初始化成功")
    except Exception as e:
        logger.error(f"特征提取器初始化失败: {e}")
        return
    
    # 2. 初始化门控融合模型
    print("\n2. 初始化门控融合模型...")
    model = GatedFusionModel(
        esm_dim=1280,      # ESM-2 t33模型的特征维度
        msa_dim=256,       # MSA特征维度
        hidden_dim=512,    # 隐藏层维度
        num_layers=2,      # Transformer层数
        dropout=0.1        # Dropout率
    )
    
    # 打印模型摘要
    input_shapes = {
        "esm_features": (1, "seq_len", 1280),
        "msa_features": {"pssm": ("seq_len", 20), "conservation_scores": ("seq_len",)}
    }
    print_model_summary(model, input_shapes)
    
    # 3. 处理示例序列
    for i, sequence in enumerate(test_sequences):
        print(f"\n{'='*60}")
        print(f"处理序列 {i+1}: {sequence[:30]}...")
        print(f"序列长度: {len(sequence)}")
        
        # 验证序列
        if not validate_sequence(sequence):
            print("❌ 序列格式无效，跳过")
            continue
        
        try:
            # 提取和融合特征
            print("正在提取ESM-2和MSA特征...")
            results = model.extract_and_fuse(sequence, extractor)
            
            print("✓ 特征提取和融合完成")
            
            # 分析结果
            print("\n特征融合结果:")
            print(f"  融合特征形状: {results['fused_features'].shape}")
            print(f"  门控值形状: {results['gate_values'].shape}")
            
            # 分析特征贡献度
            contribution_analysis = analyze_feature_contributions(results)
            print(f"\n特征贡献度分析:")
            print(f"  ESM-2平均权重: {contribution_analysis['mean_esm2_weight']:.3f}")
            print(f"  MSA平均权重: {contribution_analysis['mean_msa_weight']:.3f}")
            print(f"  门控值标准差: {contribution_analysis['gate_std']:.3f}")
            print(f"  ESM-2主导位置比例: {contribution_analysis['esm2_dominant_ratio']:.3f}")
            print(f"  MSA主导位置比例: {contribution_analysis['msa_dominant_ratio']:.3f}")
            print(f"  平衡位置比例: {contribution_analysis['balanced_ratio']:.3f}")
            
            # 可视化门控值（仅对较短序列）
            if len(sequence) <= 100:
                print("\n生成门控值可视化...")
                try:
                    visualize_gate_values(
                        results['gate_values'][0], 
                        sequence, 
                        save_path=f"gate_values_seq_{i+1}.png"
                    )
                    print("✓ 门控值可视化已保存")
                except Exception as e:
                    print(f"⚠ 可视化生成失败: {e}")
            
            # 保存特征（可选）
            # save_features(results, f"features_seq_{i+1}.npz")
            
        except Exception as e:
            logger.error(f"处理序列 {i+1} 时出错: {e}")
            continue
    
    # 4. 演示批量处理
    print(f"\n{'='*60}")
    print("演示批量处理...")
    
    try:
        from utils import batch_process_sequences
        
        batch_results = batch_process_sequences(
            test_sequences, 
            model, 
            extractor, 
            batch_size=2
        )
        
        successful_results = [r for r in batch_results if r is not None]
        print(f"✓ 批量处理完成，成功处理 {len(successful_results)}/{len(test_sequences)} 个序列")
        
    except Exception as e:
        logger.error(f"批量处理失败: {e}")
    
    print(f"\n{'='*80}")
    print("示例运行完成！")
    print("=" * 80)


def demonstrate_model_features():
    """演示模型的高级功能"""
    
    print("\n" + "="*60)
    print("高级功能演示")
    print("="*60)
    
    # 创建简单的测试数据
    batch_size, seq_len, esm_dim, msa_dim = 2, 50, 1280, 256
    
    # 模拟输入数据
    esm_features = torch.randn(batch_size, seq_len, esm_dim)
    msa_features = {
        "pssm": torch.randn(seq_len, 20),
        "conservation_scores": torch.randn(seq_len),
        "coevolution_matrix": torch.randn(seq_len, seq_len),
        "num_sequences": torch.tensor(100.0)
    }
    
    # 初始化模型
    model = GatedFusionModel(esm_dim=esm_dim, msa_dim=msa_dim, hidden_dim=512)
    model.eval()
    
    print("1. 前向传播测试...")
    with torch.no_grad():
        results = model(esm_features, msa_features)
        print(f"   输出特征形状: {results['fused_features'].shape}")
        print(f"   门控值形状: {results['gate_values'].shape}")
    
    print("\n2. 特征重要性分析...")
    importance = model.get_feature_importance(results['gate_values'])
    for key, value in importance.items():
        print(f"   {key}: {value}")
    
    print("\n3. 模型参数统计...")
    total_params = sum(p.numel() for p in model.parameters())
    print(f"   总参数量: {total_params:,}")
    
    print("✓ 高级功能演示完成")


if __name__ == "__main__":
    main()
    demonstrate_model_features()
