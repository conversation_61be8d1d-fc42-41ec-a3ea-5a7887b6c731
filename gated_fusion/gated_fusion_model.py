"""
门控融合蛋白质特征提取模型
实现ESM-2和MSA特征的门控融合
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class GatingUnit(nn.Module):
    """门控单元，用于动态权衡不同特征的重要性"""
    
    def __init__(self, esm_dim: int, msa_dim: int, hidden_dim: int = 256):
        """
        初始化门控单元
        
        Args:
            esm_dim: ESM-2特征维度
            msa_dim: MSA特征维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.hidden_dim = hidden_dim
        
        # 特征投影层，将不同维度的特征映射到相同维度
        self.esm_projection = nn.Linear(esm_dim, hidden_dim)
        self.msa_projection = nn.Linear(msa_dim, hidden_dim)
        
        # 门控网络
        self.gate_network = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Linear(hidden_dim // 2, 1),
            nn.Sigmoid()
        )
        
        # 输出投影层
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, esm_features: torch.Tensor, msa_features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        Args:
            esm_features: ESM-2特征 (batch_size, seq_len, esm_dim)
            msa_features: MSA特征 (batch_size, seq_len, msa_dim)
            
        Returns:
            融合后的特征和门控值
        """
        # 投影到相同维度
        esm_proj = self.esm_projection(esm_features)  # (batch_size, seq_len, hidden_dim)
        msa_proj = self.msa_projection(msa_features)  # (batch_size, seq_len, hidden_dim)
        
        # 拼接特征用于门控计算
        concat_features = torch.cat([esm_proj, msa_proj], dim=-1)  # (batch_size, seq_len, hidden_dim * 2)
        
        # 计算门控值
        gate_values = self.gate_network(concat_features)  # (batch_size, seq_len, 1)
        
        # 门控融合
        fused_features = gate_values * esm_proj + (1 - gate_values) * msa_proj
        
        # 输出投影
        output_features = self.output_projection(fused_features)
        
        return output_features, gate_values.squeeze(-1)


class MSAFeatureProcessor(nn.Module):
    """MSA特征处理器，将多种MSA特征融合成统一表示"""
    
    def __init__(self, output_dim: int = 256):
        """
        初始化MSA特征处理器
        
        Args:
            output_dim: 输出特征维度
        """
        super().__init__()
        
        self.output_dim = output_dim
        
        # PSSM处理
        self.pssm_processor = nn.Sequential(
            nn.Linear(20, 64),
            nn.ReLU(),
            nn.Linear(64, 128)
        )
        
        # 保守性分数处理
        self.conservation_processor = nn.Sequential(
            nn.Linear(1, 32),
            nn.ReLU(),
            nn.Linear(32, 64)
        )
        
        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Linear(128 + 64, output_dim),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(output_dim, output_dim)
        )
        
    def forward(self, msa_features: Dict[str, torch.Tensor]) -> torch.Tensor:
        """
        处理MSA特征
        
        Args:
            msa_features: MSA特征字典
            
        Returns:
            处理后的MSA特征
        """
        # 处理PSSM
        pssm_feat = self.pssm_processor(msa_features["pssm"])  # (seq_len, 128)
        
        # 处理保守性分数
        conservation_feat = self.conservation_processor(
            msa_features["conservation_scores"].unsqueeze(-1)
        )  # (seq_len, 64)
        
        # 融合特征
        combined_feat = torch.cat([pssm_feat, conservation_feat], dim=-1)
        output_feat = self.feature_fusion(combined_feat)
        
        return output_feat


class GatedFusionModel(nn.Module):
    """门控融合模型主类"""
    
    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, hidden_dim: int = 512, 
                 num_layers: int = 2, dropout: float = 0.1):
        """
        初始化门控融合模型
        
        Args:
            esm_dim: ESM-2特征维度
            msa_dim: MSA特征维度
            hidden_dim: 隐藏层维度
            num_layers: Transformer层数
            dropout: Dropout率
        """
        super().__init__()
        
        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.hidden_dim = hidden_dim
        
        # MSA特征处理器
        self.msa_processor = MSAFeatureProcessor(msa_dim)
        
        # 门控单元
        self.gating_unit = GatingUnit(esm_dim, msa_dim, hidden_dim)
        
        # 后处理Transformer层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_dim,
            nhead=8,
            dim_feedforward=hidden_dim * 2,
            dropout=dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # 位置编码
        self.pos_encoding = nn.Parameter(torch.randn(1000, hidden_dim) * 0.1)
        
    def forward(self, esm_features: torch.Tensor, msa_features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            esm_features: ESM-2残基嵌入 (batch_size, seq_len, esm_dim)
            msa_features: MSA特征字典
            
        Returns:
            融合后的特征字典
        """
        batch_size, seq_len, _ = esm_features.shape
        
        # 处理MSA特征
        processed_msa = self.msa_processor(msa_features)  # (seq_len, msa_dim)
        processed_msa = processed_msa.unsqueeze(0).expand(batch_size, -1, -1)  # (batch_size, seq_len, msa_dim)
        
        # 门控融合
        fused_features, gate_values = self.gating_unit(esm_features, processed_msa)
        
        # 添加位置编码
        if seq_len <= self.pos_encoding.size(0):
            pos_enc = self.pos_encoding[:seq_len].unsqueeze(0)
            fused_features = fused_features + pos_enc
        
        # Transformer编码
        encoded_features = self.transformer_encoder(fused_features)
        
        return {
            "fused_features": encoded_features,
            "gate_values": gate_values,
            "esm_contribution": gate_values.mean(dim=1),  # 每个序列的平均ESM贡献度
            "msa_contribution": 1 - gate_values.mean(dim=1)  # 每个序列的平均MSA贡献度
        }
    
    def extract_and_fuse(self, sequence: str, feature_extractor, msa_database_path: str = None) -> Dict[str, torch.Tensor]:
        """
        端到端特征提取和融合
        
        Args:
            sequence: 蛋白质序列
            feature_extractor: 特征提取器实例
            msa_database_path: MSA数据库路径
            
        Returns:
            融合后的特征
        """
        self.eval()
        
        with torch.no_grad():
            # 提取特征
            all_features = feature_extractor.extract_features(sequence, msa_database_path)
            
            # 准备输入
            esm_features = all_features["esm2"]["residue_embeddings"].unsqueeze(0)  # (1, seq_len, esm_dim)
            msa_features = all_features["msa"]
            
            # 移动到正确的设备
            device = next(self.parameters()).device
            esm_features = esm_features.to(device)
            msa_features = {k: v.to(device) if isinstance(v, torch.Tensor) else v 
                           for k, v in msa_features.items()}
            
            # 前向传播
            results = self.forward(esm_features, msa_features)
            
            # 添加原始特征信息
            results.update({
                "sequence": sequence,
                "sequence_length": len(sequence),
                "esm2_features": all_features["esm2"],
                "msa_features": all_features["msa"]
            })
            
            return results
    
    def get_feature_importance(self, gate_values: torch.Tensor) -> Dict[str, float]:
        """
        分析特征重要性
        
        Args:
            gate_values: 门控值 (batch_size, seq_len)
            
        Returns:
            特征重要性统计
        """
        gate_mean = gate_values.mean().item()
        gate_std = gate_values.std().item()
        
        return {
            "esm2_importance": gate_mean,
            "msa_importance": 1 - gate_mean,
            "gate_variability": gate_std,
            "esm2_dominant_positions": (gate_values > 0.7).sum().item(),
            "msa_dominant_positions": (gate_values < 0.3).sum().item(),
            "balanced_positions": ((gate_values >= 0.3) & (gate_values <= 0.7)).sum().item()
        }
