"""
门控融合模型的工具函数
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


def visualize_gate_values(gate_values: torch.Tensor, sequence: str, 
                         save_path: Optional[str] = None, figsize: Tuple[int, int] = (12, 6)):
    """
    可视化门控值
    
    Args:
        gate_values: 门控值 (seq_len,)
        sequence: 蛋白质序列
        save_path: 保存路径
        figsize: 图像大小
    """
    gate_values = gate_values.cpu().numpy() if isinstance(gate_values, torch.Tensor) else gate_values
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=figsize)
    
    # 门控值曲线图
    positions = np.arange(len(sequence))
    ax1.plot(positions, gate_values, 'b-', linewidth=2, label='Gate Values')
    ax1.axhline(y=0.5, color='r', linestyle='--', alpha=0.7, label='Balanced (0.5)')
    ax1.axhline(y=0.7, color='g', linestyle='--', alpha=0.7, label='ESM-2 Dominant (0.7)')
    ax1.axhline(y=0.3, color='orange', linestyle='--', alpha=0.7, label='MSA Dominant (0.3)')
    ax1.set_xlabel('Residue Position')
    ax1.set_ylabel('Gate Value (ESM-2 Weight)')
    ax1.set_title('Gate Values Along Protein Sequence')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 热图显示
    gate_matrix = gate_values.reshape(1, -1)
    im = ax2.imshow(gate_matrix, cmap='RdYlBu_r', aspect='auto', vmin=0, vmax=1)
    ax2.set_xlabel('Residue Position')
    ax2.set_ylabel('Gate Value')
    ax2.set_title('Gate Values Heatmap')
    
    # 添加氨基酸标签
    if len(sequence) <= 50:  # 只在序列较短时显示氨基酸
        ax2.set_xticks(range(len(sequence)))
        ax2.set_xticklabels(list(sequence))
    
    plt.colorbar(im, ax=ax2, label='ESM-2 Weight')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Gate values visualization saved to {save_path}")
    
    plt.show()


def analyze_feature_contributions(results: Dict[str, torch.Tensor]) -> Dict[str, float]:
    """
    分析特征贡献度
    
    Args:
        results: 模型输出结果
        
    Returns:
        特征贡献度分析结果
    """
    gate_values = results["gate_values"]
    if isinstance(gate_values, torch.Tensor):
        gate_values = gate_values.cpu().numpy()
    
    analysis = {
        "mean_esm2_weight": float(np.mean(gate_values)),
        "mean_msa_weight": float(1 - np.mean(gate_values)),
        "gate_std": float(np.std(gate_values)),
        "gate_min": float(np.min(gate_values)),
        "gate_max": float(np.max(gate_values)),
        "esm2_dominant_ratio": float(np.mean(gate_values > 0.7)),
        "msa_dominant_ratio": float(np.mean(gate_values < 0.3)),
        "balanced_ratio": float(np.mean((gate_values >= 0.3) & (gate_values <= 0.7)))
    }
    
    return analysis


def compare_features(esm2_features: torch.Tensor, msa_features: torch.Tensor, 
                    fused_features: torch.Tensor, method: str = "cosine") -> Dict[str, float]:
    """
    比较不同特征之间的相似性
    
    Args:
        esm2_features: ESM-2特征
        msa_features: MSA特征
        fused_features: 融合后特征
        method: 相似性计算方法
        
    Returns:
        相似性分析结果
    """
    def compute_similarity(feat1, feat2, method):
        if method == "cosine":
            return torch.nn.functional.cosine_similarity(feat1, feat2, dim=-1).mean().item()
        elif method == "l2":
            return -torch.norm(feat1 - feat2, dim=-1).mean().item()
        else:
            raise ValueError(f"Unknown similarity method: {method}")
    
    # 确保特征在同一设备上
    device = fused_features.device
    esm2_features = esm2_features.to(device)
    msa_features = msa_features.to(device)
    
    # 投影到相同维度进行比较
    if esm2_features.shape[-1] != fused_features.shape[-1]:
        proj = torch.nn.Linear(esm2_features.shape[-1], fused_features.shape[-1]).to(device)
        esm2_features = proj(esm2_features)
    
    if msa_features.shape[-1] != fused_features.shape[-1]:
        proj = torch.nn.Linear(msa_features.shape[-1], fused_features.shape[-1]).to(device)
        msa_features = proj(msa_features)
    
    similarities = {
        f"esm2_vs_fused_{method}": compute_similarity(esm2_features, fused_features, method),
        f"msa_vs_fused_{method}": compute_similarity(msa_features, fused_features, method),
        f"esm2_vs_msa_{method}": compute_similarity(esm2_features, msa_features, method)
    }
    
    return similarities


def save_features(features: Dict[str, torch.Tensor], save_path: str):
    """
    保存特征到文件
    
    Args:
        features: 特征字典
        save_path: 保存路径
    """
    # 转换为numpy格式保存
    features_np = {}
    for key, value in features.items():
        if isinstance(value, torch.Tensor):
            features_np[key] = value.cpu().numpy()
        else:
            features_np[key] = value
    
    np.savez_compressed(save_path, **features_np)
    logger.info(f"Features saved to {save_path}")


def load_features(load_path: str) -> Dict[str, torch.Tensor]:
    """
    从文件加载特征
    
    Args:
        load_path: 文件路径
        
    Returns:
        特征字典
    """
    data = np.load(load_path, allow_pickle=True)
    features = {}
    
    for key in data.files:
        value = data[key]
        if isinstance(value, np.ndarray) and value.dtype != object:
            features[key] = torch.from_numpy(value)
        else:
            features[key] = value
    
    logger.info(f"Features loaded from {load_path}")
    return features


def print_model_summary(model, input_shapes: Dict[str, Tuple]):
    """
    打印模型摘要
    
    Args:
        model: 模型实例
        input_shapes: 输入形状字典
    """
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print("=" * 60)
    print("MODEL SUMMARY")
    print("=" * 60)
    print(f"Model: {model.__class__.__name__}")
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    print(f"Non-trainable parameters: {total_params - trainable_params:,}")
    print("\nInput shapes:")
    for name, shape in input_shapes.items():
        print(f"  {name}: {shape}")
    print("=" * 60)


def validate_sequence(sequence: str) -> bool:
    """
    验证蛋白质序列格式
    
    Args:
        sequence: 蛋白质序列
        
    Returns:
        是否为有效序列
    """
    valid_aa = set("ACDEFGHIKLMNPQRSTVWY")
    sequence = sequence.upper().strip()
    
    if not sequence:
        return False
    
    if not all(aa in valid_aa for aa in sequence):
        invalid_chars = set(sequence) - valid_aa
        logger.warning(f"Invalid amino acids found: {invalid_chars}")
        return False
    
    if len(sequence) < 10:
        logger.warning("Sequence too short (< 10 residues)")
        return False
    
    return True


def batch_process_sequences(sequences: List[str], model, feature_extractor, 
                          batch_size: int = 8, msa_database_path: str = None) -> List[Dict]:
    """
    批量处理蛋白质序列
    
    Args:
        sequences: 蛋白质序列列表
        model: 融合模型
        feature_extractor: 特征提取器
        batch_size: 批次大小
        msa_database_path: MSA数据库路径
        
    Returns:
        处理结果列表
    """
    results = []
    
    for i in range(0, len(sequences), batch_size):
        batch_sequences = sequences[i:i+batch_size]
        batch_results = []
        
        for seq in batch_sequences:
            if validate_sequence(seq):
                try:
                    result = model.extract_and_fuse(seq, feature_extractor, msa_database_path)
                    batch_results.append(result)
                except Exception as e:
                    logger.error(f"Error processing sequence {seq[:20]}...: {e}")
                    batch_results.append(None)
            else:
                logger.warning(f"Invalid sequence skipped: {seq[:20]}...")
                batch_results.append(None)
        
        results.extend(batch_results)
        logger.info(f"Processed batch {i//batch_size + 1}/{(len(sequences)-1)//batch_size + 1}")
    
    return results
