"""
架构级融合模型使用示例
"""

import torch
import logging
from feature_extractor import ProteinFeatureExtractor
from evoformer_model import EvoformerModel
from utils import (
    visualize_pair_representation,
    visualize_contact_map,
    visualize_msa_representation,
    plot_representation_evolution,
    print_evoformer_summary,
    analyze_evoformer_outputs
)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """主函数，演示架构级融合模型的使用"""
    
    # 示例蛋白质序列
    test_sequences = [
        "MKTVRQERLKSIVRILERSKEPVSGAQLAEELSVSRQVIVQDIAYLRSLGYNIVATPRGYVLAGG",
        "MKWVTFISLLFLFSSAYSRGVFRRDAHKSEVAHRFKDLGEENFKALVLIAFAQYLQQCPFEDHVKLVNEVTEFAKTCVADESAENCDKSLHTLFGDKLCTVATLRETYGEMADCCAKQEPERNECFLQHKDDNPNLPRLVRPEVDVMCTAFHDNEETFLKKYLYEIARRHPYFYAPELLFFAKRYKAAFTECCQAADKAACLLPKLDELRDEGKASSAKQRLKCASLQKFGERAFKAWAVARLSQRFPKAEFAEVSKLVTDLTKVHTECCHGDLLECADDRADLAKYICENQDSISSKLKECCEKPLLEKSHCIAEVENDEMPADLPSLAADFVESKDVCKNYAEAKDVFLGMFLYEYARRHPDYSVVLLLRLAKTYETTLEKCCAAADPHECYAKVFDEFKPLVEEPQNLIKQNCELFEQLGEYKFQNALLVRYTKKVPQVSTPTLVEVSRNLGKVGSKCCKHPEAKRMPCAEDYLSVVLNQLCVLHEKTPVSDRVTKCCTESLVNRRPCFSALEVDETYVPKEFNAETFTFHADICTLSEKERQIKKQTALVELVKHKPKATKEQLKAVMDDFAAFVEKCCKADDKETCFAEEGKKLVAASQAALGL"
    ]
    
    print("=" * 80)
    print("架构级融合蛋白质特征提取模型示例 (Evoformer风格)")
    print("=" * 80)
    
    # 1. 初始化特征提取器
    print("\n1. 初始化特征提取器...")
    try:
        extractor = ProteinFeatureExtractor(esm_model_name="facebook/esm2_t33_650M_UR50D")
        print("✓ 特征提取器初始化成功")
    except Exception as e:
        logger.error(f"特征提取器初始化失败: {e}")
        return
    
    # 2. 初始化Evoformer模型
    print("\n2. 初始化Evoformer模型...")
    model = EvoformerModel(
        esm_dim=1280,      # ESM-2 t33模型的特征维度
        msa_dim=256,       # MSA特征维度
        pair_dim=128,      # Pair特征维度
        num_blocks=4,      # Evoformer块数量
        num_heads=8,       # 注意力头数
        dropout=0.1        # Dropout率
    )
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    print(f"模型总参数量: {total_params:,}")
    
    # 3. 处理示例序列
    for i, sequence in enumerate(test_sequences):
        print(f"\n{'='*60}")
        print(f"处理序列 {i+1}: {sequence[:30]}...")
        print(f"序列长度: {len(sequence)}")
        
        try:
            # 提取和融合特征
            print("正在提取ESM-2和MSA特征，构建三种表示...")
            results = model.extract_and_fuse(sequence, extractor)
            
            print("✓ 特征提取和融合完成")
            
            # 打印结果摘要
            print_evoformer_summary(results)
            
            # 分析表示质量
            analysis = analyze_evoformer_outputs(results)
            print("\n详细分析:")
            for key, value in analysis.items():
                print(f"  {key}: {value:.4f}")
            
            # 结构预测
            if "pair_representation" in results:
                print("\n进行结构预测...")
                structure_predictions = model.get_structure_predictions(results["pair_representation"])
                
                contact_probs = structure_predictions["contact_probabilities"]
                print(f"  接触图形状: {contact_probs.shape}")
                print(f"  平均接触概率: {contact_probs.mean().item():.4f}")
                print(f"  高置信度接触数 (>0.8): {(contact_probs > 0.8).sum().item()}")
            
            # 可视化（仅对较短序列）
            if len(sequence) <= 100:
                print("\n生成可视化...")
                try:
                    # 可视化Pair representation
                    if "pair_representation" in results:
                        visualize_pair_representation(
                            results["pair_representation"][0],
                            sequence,
                            title=f"Pair Representation (Seq {i+1})",
                            save_path=f"pair_representation_seq_{i+1}.png"
                        )
                    
                    # 可视化接触图
                    if "pair_representation" in results:
                        structure_pred = model.get_structure_predictions(results["pair_representation"])
                        visualize_contact_map(
                            structure_pred["contact_probabilities"][0],
                            sequence,
                            save_path=f"contact_map_seq_{i+1}.png"
                        )
                    
                    # 可视化MSA representation
                    if "msa_representation" in results:
                        visualize_msa_representation(
                            results["msa_representation"][0],
                            sequence,
                            save_path=f"msa_representation_seq_{i+1}.png"
                        )
                    
                    print("✓ 可视化已保存")
                except Exception as e:
                    print(f"⚠ 可视化生成失败: {e}")
            
            # 分析表示演化
            if "msa_evolution" in results and len(results["msa_evolution"]) > 1:
                print("\n分析表示演化...")
                try:
                    plot_representation_evolution(
                        results["msa_evolution"],
                        "msa",
                        save_path=f"msa_evolution_seq_{i+1}.png"
                    )
                    
                    plot_representation_evolution(
                        results["pair_evolution"],
                        "pair",
                        save_path=f"pair_evolution_seq_{i+1}.png"
                    )
                    
                    print("✓ 演化分析完成")
                except Exception as e:
                    print(f"⚠ 演化分析失败: {e}")
            
        except Exception as e:
            logger.error(f"处理序列 {i+1} 时出错: {e}")
            continue
    
    # 4. 演示模型的高级功能
    print(f"\n{'='*60}")
    print("高级功能演示")
    print("="*60)
    
    demonstrate_evoformer_components(model)
    
    print(f"\n{'='*80}")
    print("示例运行完成！")
    print("=" * 80)


def demonstrate_evoformer_components(model):
    """演示Evoformer组件的工作原理"""
    
    print("\n1. Evoformer组件测试...")
    
    # 创建测试数据
    batch_size, seq_len, num_sequences = 1, 50, 32
    esm_dim, msa_dim, pair_dim = 1280, 256, 128
    
    # 模拟输入数据
    single_repr = torch.randn(batch_size, seq_len, esm_dim)
    msa_repr = torch.randn(batch_size, num_sequences, seq_len, 21)  # 21 = 20 AA + gap
    pair_repr = torch.randn(batch_size, seq_len, seq_len, 128)  # 初始pair representation
    
    print("✓ 测试数据创建完成")
    
    # 前向传播
    model.eval()
    with torch.no_grad():
        results = model(single_repr, msa_repr, pair_repr)
    
    print("✓ 前向传播完成")
    
    # 分析输出
    print(f"\n输出分析:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
        elif isinstance(value, list):
            print(f"  {key}: {len(value)} layers")
    
    # 分析表示演化
    if "msa_evolution" in results:
        msa_evolution = results["msa_evolution"]
        print(f"\nMSA表示演化:")
        for i, layer_repr in enumerate(msa_evolution):
            mean_val = layer_repr.mean().item()
            std_val = layer_repr.std().item()
            print(f"  Layer {i}: mean={mean_val:.4f}, std={std_val:.4f}")
    
    # 测试结构预测
    print(f"\n2. 结构预测测试...")
    structure_pred = model.get_structure_predictions(results["pair_representation"])
    
    contact_probs = structure_pred["contact_probabilities"]
    print(f"  接触概率形状: {contact_probs.shape}")
    print(f"  接触概率范围: [{contact_probs.min().item():.4f}, {contact_probs.max().item():.4f}]")
    print(f"  对称性检查: {torch.allclose(contact_probs, contact_probs.transpose(-1, -2), atol=1e-6)}")
    
    # 测试表示分析
    print(f"\n3. 表示分析测试...")
    analysis = model.analyze_representations(results)
    
    print("  表示统计:")
    for key, value in analysis.items():
        print(f"    {key}: {value:.4f}")
    
    print("✓ 高级功能演示完成")


def compare_architectural_fusion():
    """比较架构级融合与其他方法的优势"""
    
    print("\n" + "="*60)
    print("架构级融合方法比较")
    print("="*60)
    
    print("Evoformer架构级融合的优势:")
    print("  ✓ 双流网络设计，充分利用不同特征的特性")
    print("  ✓ 多层信息交换，实现深度特征融合")
    print("  ✓ 三角注意力和三角乘法，捕获复杂的残基对关系")
    print("  ✓ 外积均值操作，有效整合MSA信息到Pair表示")
    print("  ✓ 可直接用于结构预测任务")
    
    print("\n架构级融合的考虑:")
    print("  ⚠ 模型复杂度最高，训练成本大")
    print("  ⚠ 需要大量的计算资源")
    print("  ⚠ 对MSA质量要求较高")
    print("  ⚠ 参数量庞大，容易过拟合")
    
    print("\n适用场景:")
    print("  • 蛋白质结构预测")
    print("  • 蛋白质-蛋白质相互作用预测")
    print("  • 需要残基对信息的任务")
    print("  • 有充足计算资源的研究项目")


if __name__ == "__main__":
    main()
    compare_architectural_fusion()
