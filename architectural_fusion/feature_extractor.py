"""
蛋白质特征提取器 - 架构级融合版本
支持ESM-2和MSA特征提取，针对Evoformer风格的架构级融合进行优化
"""

import torch
import torch.nn as nn
import numpy as np
from transformers import EsmModel, EsmTokenizer
import subprocess
import tempfile
import os
from typing import Dict, List, Tuple, Optional
from Bio import SeqIO
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EvoformerFeatureExtractor:
    """Evoformer风格的特征提取器"""
    
    def __init__(self, esm_model_name: str = "facebook/esm2_t33_650M_UR50D"):
        """
        初始化特征提取器
        
        Args:
            esm_model_name: ESM-2模型名称
        """
        self.esm_model_name = esm_model_name
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载ESM-2模型
        logger.info(f"Loading ESM-2 model: {esm_model_name}")
        self.esm_tokenizer = EsmTokenizer.from_pretrained(esm_model_name)
        self.esm_model = EsmModel.from_pretrained(esm_model_name)
        self.esm_model.to(self.device)
        self.esm_model.eval()
        
        self.esm_dim = self.esm_model.config.hidden_size
        
    def extract_esm2_features(self, sequence: str) -> Dict[str, torch.Tensor]:
        """
        提取ESM-2特征，包括单序列表示
        
        Args:
            sequence: 蛋白质序列
            
        Returns:
            ESM-2特征字典
        """
        inputs = self.esm_tokenizer(sequence, return_tensors="pt", padding=True, truncation=True)
        inputs = {k: v.to(self.device) for k, v in inputs.items()}
        
        with torch.no_grad():
            outputs = self.esm_model(**inputs, output_attentions=True, output_hidden_states=True)
        
        features = {
            # 单序列表示 (用于Evoformer的single representation)
            "single_representation": outputs.last_hidden_state[0, 1:-1, :],  # (L, D)
            # 序列级嵌入
            "sequence_embedding": outputs.last_hidden_state[0, 0, :],  # (D,)
            # 注意力图 (可用于初始化pair representation)
            "attention_maps": torch.stack([layer_attention[0] for layer_attention in outputs.attentions]),
            # 所有隐藏状态
            "hidden_states": torch.stack([hidden_state[0] for hidden_state in outputs.hidden_states]),
        }
        
        # 移除CLS和SEP token
        features["attention_maps"] = features["attention_maps"][:, :, 1:-1, 1:-1]
        features["hidden_states"] = features["hidden_states"][:, 1:-1, :]
        
        return features
    
    def generate_msa_with_hhblits(self, sequence: str, database_path: str = None,
                                  max_sequences: int = 512) -> List[str]:
        """
        使用HHblits生成MSA
        
        Args:
            sequence: 查询序列
            database_path: 数据库路径
            max_sequences: 最大序列数量
            
        Returns:
            MSA序列列表
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix='.fasta', delete=False) as query_file:
            query_file.write(f">query\n{sequence}\n")
            query_path = query_file.name
        
        msa_path = query_path.replace('.fasta', '.a3m')
        
        try:
            cmd = [
                "hhblits",
                "-i", query_path,
                "-o", msa_path,
                "-n", "3",
                "-e", "1e-3",
                "-maxseq", str(max_sequences)
            ]
            
            if database_path:
                cmd.extend(["-d", database_path])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                logger.warning("HHblits failed, creating single-sequence MSA")
                sequences = [sequence]
            else:
                # 读取MSA
                sequences = []
                try:
                    with open(msa_path, 'r') as f:
                        for record in SeqIO.parse(f, "fasta"):
                            seq_str = str(record.seq)
                            # 保留gap信息用于MSA处理
                            sequences.append(seq_str)
                except:
                    sequences = [sequence]
            
            # 确保查询序列在第一位
            if sequences and sequences[0].replace('-', '') != sequence:
                # 找到查询序列并移到第一位
                query_found = False
                for i, seq in enumerate(sequences):
                    if seq.replace('-', '') == sequence:
                        sequences[0], sequences[i] = sequences[i], sequences[0]
                        query_found = True
                        break
                
                if not query_found:
                    sequences.insert(0, sequence)
            
            return sequences[:max_sequences] if sequences else [sequence]
            
        except Exception as e:
            logger.warning(f"MSA generation failed: {e}")
            return [sequence]
        
        finally:
            for path in [query_path, msa_path]:
                if os.path.exists(path):
                    os.unlink(path)
    
    def create_msa_representation(self, msa_sequences: List[str], 
                                 max_sequences: int = 512) -> torch.Tensor:
        """
        创建MSA表示矩阵
        
        Args:
            msa_sequences: MSA序列列表
            max_sequences: 最大序列数量
            
        Returns:
            MSA表示矩阵 (num_sequences, seq_len, 21) - 20个氨基酸 + gap
        """
        if len(msa_sequences) > max_sequences:
            msa_sequences = msa_sequences[:max_sequences]
        
        # 氨基酸到索引的映射 (包括gap)
        aa_to_idx = {aa: i for i, aa in enumerate("ACDEFGHIKLMNPQRSTVWY-")}
        
        # 找到最大序列长度
        max_len = max(len(seq) for seq in msa_sequences)
        
        # 创建MSA矩阵
        num_sequences = len(msa_sequences)
        msa_matrix = torch.zeros(num_sequences, max_len, 21)
        
        for i, sequence in enumerate(msa_sequences):
            for j, aa in enumerate(sequence):
                if j < max_len and aa.upper() in aa_to_idx:
                    msa_matrix[i, j, aa_to_idx[aa.upper()]] = 1.0
                elif j < max_len:
                    # 未知氨基酸，使用均匀分布
                    msa_matrix[i, j, :20] = 1.0 / 20
        
        return msa_matrix
    
    def create_pair_representation(self, sequence: str, msa_sequences: List[str] = None,
                                  esm_attention: torch.Tensor = None) -> torch.Tensor:
        """
        创建初始的pair representation
        
        Args:
            sequence: 查询序列
            msa_sequences: MSA序列列表
            esm_attention: ESM-2注意力图
            
        Returns:
            Pair representation (seq_len, seq_len, pair_dim)
        """
        seq_len = len(sequence)
        pair_dim = 128  # 初始pair representation维度
        
        # 初始化pair representation
        pair_repr = torch.zeros(seq_len, seq_len, pair_dim)
        
        # 1. 位置编码
        for i in range(seq_len):
            for j in range(seq_len):
                # 相对位置编码
                rel_pos = abs(i - j)
                if rel_pos < 32:  # 限制相对位置编码的范围
                    pair_repr[i, j, rel_pos] = 1.0
                
                # 距离编码
                if rel_pos == 0:
                    pair_repr[i, j, 32] = 1.0  # 对角线
                elif rel_pos == 1:
                    pair_repr[i, j, 33] = 1.0  # 相邻位置
        
        # 2. 使用ESM-2注意力初始化
        if esm_attention is not None:
            # 使用最后一层的平均注意力
            avg_attention = esm_attention[-1].mean(dim=0)  # 平均多头注意力
            if avg_attention.shape[0] == seq_len and avg_attention.shape[1] == seq_len:
                # 将注意力权重映射到pair representation的一部分维度
                pair_repr[:, :, 34:42] = avg_attention.unsqueeze(-1).expand(-1, -1, 8)
        
        # 3. 使用MSA共进化信息
        if msa_sequences and len(msa_sequences) > 1:
            coev_matrix = self._calculate_coevolution_matrix(msa_sequences)
            if coev_matrix.shape[0] == seq_len and coev_matrix.shape[1] == seq_len:
                pair_repr[:, :, 42:50] = torch.tensor(coev_matrix, dtype=torch.float32).unsqueeze(-1).expand(-1, -1, 8)
        
        return pair_repr
    
    def _calculate_coevolution_matrix(self, msa_sequences: List[str]) -> np.ndarray:
        """计算共进化矩阵"""
        if not msa_sequences:
            return np.zeros((100, 100))  # 默认大小
        
        # 移除gap并获取查询序列长度
        query_seq = msa_sequences[0].replace('-', '')
        seq_len = len(query_seq)
        
        # 创建对齐的序列矩阵
        aligned_sequences = []
        for seq in msa_sequences:
            # 保持对齐，但记录非gap位置
            aligned_sequences.append(seq)
        
        coev_matrix = np.zeros((seq_len, seq_len))
        
        # 简化的互信息计算
        for i in range(seq_len):
            for j in range(i + 1, seq_len):
                # 计算位置i和j的氨基酸对频率
                pair_counts = {}
                total_pairs = 0
                
                for seq in aligned_sequences:
                    if i < len(seq) and j < len(seq):
                        aa_i, aa_j = seq[i], seq[j]
                        if aa_i != '-' and aa_j != '-':  # 忽略gap
                            pair = (aa_i, aa_j)
                            pair_counts[pair] = pair_counts.get(pair, 0) + 1
                            total_pairs += 1
                
                # 计算互信息
                if total_pairs > 0:
                    mi = 0
                    for count in pair_counts.values():
                        if count > 0:
                            p = count / total_pairs
                            mi += p * np.log2(p + 1e-8)
                    
                    coev_matrix[i, j] = coev_matrix[j, i] = -mi
        
        return coev_matrix
    
    def extract_features(self, sequence: str, msa_database_path: str = None,
                        max_msa_sequences: int = 512) -> Dict[str, torch.Tensor]:
        """
        提取完整的Evoformer风格特征
        
        Args:
            sequence: 蛋白质序列
            msa_database_path: MSA数据库路径
            max_msa_sequences: 最大MSA序列数量
            
        Returns:
            完整特征字典
        """
        logger.info("Extracting ESM-2 features...")
        esm_features = self.extract_esm2_features(sequence)
        
        logger.info("Generating MSA...")
        msa_sequences = self.generate_msa_with_hhblits(sequence, msa_database_path, max_msa_sequences)
        
        logger.info("Creating MSA representation...")
        msa_representation = self.create_msa_representation(msa_sequences, max_msa_sequences)
        
        logger.info("Creating pair representation...")
        pair_representation = self.create_pair_representation(
            sequence, 
            msa_sequences, 
            esm_features["attention_maps"]
        )
        
        all_features = {
            # Evoformer的三个核心表示
            "single_representation": esm_features["single_representation"],  # (L, D)
            "msa_representation": msa_representation,  # (N, L, 21)
            "pair_representation": pair_representation,  # (L, L, pair_dim)
            
            # 原始ESM-2特征
            "esm2_features": esm_features,
            
            # MSA信息
            "msa_sequences": msa_sequences,
            "msa_depth": len(msa_sequences),
            
            # 序列信息
            "sequence": sequence,
            "sequence_length": len(sequence)
        }
        
        return all_features


class ProteinFeatureExtractor(EvoformerFeatureExtractor):
    """蛋白质特征提取器的别名，保持接口一致性"""
    pass
