"""
Evoformer风格的架构级融合蛋白质特征提取模型
实现双流网络架构，参考AlphaFold2的Evoformer设计
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List
import math
import logging
from einops import rearrange, repeat

logger = logging.getLogger(__name__)


class OuterProductMean(nn.Module):
    """外积均值模块，用于从MSA更新Pair representation"""
    
    def __init__(self, msa_dim: int, pair_dim: int, hidden_dim: int = 32):
        """
        初始化外积均值模块
        
        Args:
            msa_dim: MSA特征维度
            pair_dim: Pair特征维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        
        self.msa_dim = msa_dim
        self.pair_dim = pair_dim
        self.hidden_dim = hidden_dim
        
        # 线性投影层
        self.linear_a = nn.Linear(msa_dim, hidden_dim)
        self.linear_b = nn.Linear(msa_dim, hidden_dim)
        self.linear_out = nn.Linear(hidden_dim * hidden_dim, pair_dim)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(pair_dim)
        
    def forward(self, msa_repr: torch.Tensor, pair_repr: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            msa_repr: MSA表示 (batch_size, num_sequences, seq_len, msa_dim)
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, pair_dim)
            
        Returns:
            更新后的Pair表示
        """
        batch_size, num_sequences, seq_len, _ = msa_repr.shape
        
        # 线性投影
        a = self.linear_a(msa_repr)  # (batch_size, num_sequences, seq_len, hidden_dim)
        b = self.linear_b(msa_repr)  # (batch_size, num_sequences, seq_len, hidden_dim)
        
        # 计算外积
        # a: (batch_size, num_sequences, seq_len, 1, hidden_dim)
        # b: (batch_size, num_sequences, 1, seq_len, hidden_dim)
        a = a.unsqueeze(3)
        b = b.unsqueeze(2)
        
        outer_product = a * b  # (batch_size, num_sequences, seq_len, seq_len, hidden_dim)
        
        # 沿序列维度求均值
        outer_product_mean = outer_product.mean(dim=1)  # (batch_size, seq_len, seq_len, hidden_dim)
        
        # 重塑并通过线性层
        outer_product_flat = outer_product_mean.view(batch_size, seq_len, seq_len, -1)
        update = self.linear_out(outer_product_flat)
        
        # 残差连接和层归一化
        updated_pair = self.layer_norm(pair_repr + update)
        
        return updated_pair


class TriangleMultiplication(nn.Module):
    """三角乘法模块，用于Pair representation的自更新"""
    
    def __init__(self, pair_dim: int, hidden_dim: int = 128):
        """
        初始化三角乘法模块
        
        Args:
            pair_dim: Pair特征维度
            hidden_dim: 隐藏层维度
        """
        super().__init__()
        
        self.pair_dim = pair_dim
        self.hidden_dim = hidden_dim
        
        # 门控线性层
        self.linear_a_p = nn.Linear(pair_dim, hidden_dim)
        self.linear_a_g = nn.Linear(pair_dim, hidden_dim)
        self.linear_b_p = nn.Linear(pair_dim, hidden_dim)
        self.linear_b_g = nn.Linear(pair_dim, hidden_dim)
        
        # 输出投影
        self.linear_out = nn.Linear(hidden_dim, pair_dim)
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(pair_dim)
        
    def forward(self, pair_repr: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, pair_dim)
            mask: 掩码 (batch_size, seq_len, seq_len)
            
        Returns:
            更新后的Pair表示
        """
        batch_size, seq_len, _, _ = pair_repr.shape
        
        # 计算门控投影
        a_p = self.linear_a_p(pair_repr)  # (batch_size, seq_len, seq_len, hidden_dim)
        a_g = torch.sigmoid(self.linear_a_g(pair_repr))
        b_p = self.linear_b_p(pair_repr)
        b_g = torch.sigmoid(self.linear_b_g(pair_repr))
        
        # 应用门控
        a = a_p * a_g
        b = b_p * b_g
        
        # 三角乘法: outgoing edges
        # a: (batch_size, seq_len, seq_len, hidden_dim)
        # b: (batch_size, seq_len, seq_len, hidden_dim)
        # 计算 sum_k a_ik * b_kj
        update = torch.einsum('bikd,bkjd->bijd', a, b)
        
        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(-1)
            update = update * mask
        
        # 输出投影
        update = self.linear_out(update)
        
        # 残差连接和层归一化
        updated_pair = self.layer_norm(pair_repr + update)
        
        return updated_pair


class TriangleAttention(nn.Module):
    """三角注意力模块"""
    
    def __init__(self, pair_dim: int, num_heads: int = 4, dropout: float = 0.1):
        """
        初始化三角注意力模块
        
        Args:
            pair_dim: Pair特征维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()
        
        self.pair_dim = pair_dim
        self.num_heads = num_heads
        self.head_dim = pair_dim // num_heads
        
        assert pair_dim % num_heads == 0
        
        # 注意力投影层
        self.q_proj = nn.Linear(pair_dim, pair_dim)
        self.k_proj = nn.Linear(pair_dim, pair_dim)
        self.v_proj = nn.Linear(pair_dim, pair_dim)
        self.out_proj = nn.Linear(pair_dim, pair_dim)
        
        # Dropout和层归一化
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(pair_dim)
        
        self.scale = math.sqrt(self.head_dim)
        
    def forward(self, pair_repr: torch.Tensor, mask: Optional[torch.Tensor] = None,
                dim: int = -2) -> torch.Tensor:
        """
        前向传播
        
        Args:
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, pair_dim)
            mask: 注意力掩码
            dim: 注意力计算的维度 (-2 for row-wise, -3 for column-wise)
            
        Returns:
            更新后的Pair表示
        """
        batch_size, seq_len, _, _ = pair_repr.shape
        
        # 投影到Q, K, V
        q = self.q_proj(pair_repr)
        k = self.k_proj(pair_repr)
        v = self.v_proj(pair_repr)
        
        # 重塑为多头格式
        q = rearrange(q, 'b i j (h d) -> b h i j d', h=self.num_heads)
        k = rearrange(k, 'b i j (h d) -> b h i j d', h=self.num_heads)
        v = rearrange(v, 'b i j (h d) -> b h i j d', h=self.num_heads)
        
        if dim == -2:  # Row-wise attention
            # 沿着j维度计算注意力
            attention_scores = torch.einsum('bhijd,bhikd->bhijk', q, k) / self.scale
            
            if mask is not None:
                attention_scores = attention_scores.masked_fill(mask.unsqueeze(1).unsqueeze(-1) == 0, -1e9)
            
            attention_weights = F.softmax(attention_scores, dim=-1)
            attention_weights = self.dropout(attention_weights)
            
            attended_values = torch.einsum('bhijk,bhikd->bhijd', attention_weights, v)
            
        else:  # Column-wise attention (dim == -3)
            # 沿着i维度计算注意力
            attention_scores = torch.einsum('bhijd,bhkjd->bhikj', q, k) / self.scale
            
            if mask is not None:
                attention_scores = attention_scores.masked_fill(mask.unsqueeze(1).unsqueeze(-2) == 0, -1e9)
            
            attention_weights = F.softmax(attention_scores, dim=-2)
            attention_weights = self.dropout(attention_weights)
            
            attended_values = torch.einsum('bhikj,bhkjd->bhijd', attention_weights, v)
        
        # 重塑回原始格式
        output = rearrange(attended_values, 'b h i j d -> b i j (h d)')
        
        # 输出投影
        output = self.out_proj(output)
        
        # 残差连接和层归一化
        updated_pair = self.layer_norm(pair_repr + output)

        return updated_pair


class MSARowAttention(nn.Module):
    """MSA行注意力模块"""

    def __init__(self, msa_dim: int, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化MSA行注意力模块

        Args:
            msa_dim: MSA特征维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()

        self.msa_dim = msa_dim
        self.num_heads = num_heads
        self.head_dim = msa_dim // num_heads

        assert msa_dim % num_heads == 0

        # 注意力投影层
        self.q_proj = nn.Linear(msa_dim, msa_dim)
        self.k_proj = nn.Linear(msa_dim, msa_dim)
        self.v_proj = nn.Linear(msa_dim, msa_dim)
        self.out_proj = nn.Linear(msa_dim, msa_dim)

        # Dropout和层归一化
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(msa_dim)

        self.scale = math.sqrt(self.head_dim)

    def forward(self, msa_repr: torch.Tensor, mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        前向传播

        Args:
            msa_repr: MSA表示 (batch_size, num_sequences, seq_len, msa_dim)
            mask: 注意力掩码

        Returns:
            更新后的MSA表示
        """
        batch_size, num_sequences, seq_len, _ = msa_repr.shape

        # 投影到Q, K, V
        q = self.q_proj(msa_repr)
        k = self.k_proj(msa_repr)
        v = self.v_proj(msa_repr)

        # 重塑为多头格式
        q = rearrange(q, 'b n l (h d) -> b n h l d', h=self.num_heads)
        k = rearrange(k, 'b n l (h d) -> b n h l d', h=self.num_heads)
        v = rearrange(v, 'b n l (h d) -> b n h l d', h=self.num_heads)

        # 计算注意力分数 (沿着序列长度维度)
        attention_scores = torch.einsum('bnhid,bnhjd->bnhij', q, k) / self.scale

        # 应用掩码
        if mask is not None:
            attention_scores = attention_scores.masked_fill(mask.unsqueeze(1).unsqueeze(1) == 0, -1e9)

        # Softmax归一化
        attention_weights = F.softmax(attention_scores, dim=-1)
        attention_weights = self.dropout(attention_weights)

        # 应用注意力权重
        attended_values = torch.einsum('bnhij,bnhjd->bnhid', attention_weights, v)

        # 重塑回原始格式
        output = rearrange(attended_values, 'b n h l d -> b n l (h d)')

        # 输出投影
        output = self.out_proj(output)

        # 残差连接和层归一化
        updated_msa = self.layer_norm(msa_repr + output)

        return updated_msa


class EvoformerBlock(nn.Module):
    """Evoformer块，包含MSA和Pair的更新"""

    def __init__(self, msa_dim: int, pair_dim: int, single_dim: int,
                 num_heads: int = 8, dropout: float = 0.1):
        """
        初始化Evoformer块

        Args:
            msa_dim: MSA特征维度
            pair_dim: Pair特征维度
            single_dim: Single特征维度
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()

        # MSA处理模块
        self.msa_row_attention = MSARowAttention(msa_dim, num_heads, dropout)
        self.msa_column_attention = MSARowAttention(msa_dim, num_heads, dropout)  # 复用行注意力

        # MSA到Pair的更新
        self.outer_product_mean = OuterProductMean(msa_dim, pair_dim)

        # Pair处理模块
        self.triangle_multiplication_outgoing = TriangleMultiplication(pair_dim)
        self.triangle_multiplication_incoming = TriangleMultiplication(pair_dim)
        self.triangle_attention_starting = TriangleAttention(pair_dim, num_heads//2, dropout)
        self.triangle_attention_ending = TriangleAttention(pair_dim, num_heads//2, dropout)

        # Single representation处理
        self.single_attention = nn.MultiheadAttention(single_dim, num_heads, dropout, batch_first=True)
        self.single_norm = nn.LayerNorm(single_dim)

        # 前馈网络
        self.msa_transition = nn.Sequential(
            nn.Linear(msa_dim, msa_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(msa_dim * 4, msa_dim)
        )

        self.pair_transition = nn.Sequential(
            nn.Linear(pair_dim, pair_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(pair_dim * 4, pair_dim)
        )

        self.single_transition = nn.Sequential(
            nn.Linear(single_dim, single_dim * 4),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(single_dim * 4, single_dim)
        )

        # 层归一化
        self.msa_norm = nn.LayerNorm(msa_dim)
        self.pair_norm = nn.LayerNorm(pair_dim)

    def forward(self, msa_repr: torch.Tensor, pair_repr: torch.Tensor,
                single_repr: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        前向传播

        Args:
            msa_repr: MSA表示 (batch_size, num_sequences, seq_len, msa_dim)
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, pair_dim)
            single_repr: Single表示 (batch_size, seq_len, single_dim)
            mask: 掩码

        Returns:
            更新后的MSA、Pair和Single表示
        """
        # 1. MSA处理
        # MSA行注意力
        msa_repr = self.msa_row_attention(msa_repr, mask)

        # MSA列注意力 (转置后使用行注意力)
        msa_transposed = msa_repr.transpose(1, 2)  # (batch_size, seq_len, num_sequences, msa_dim)
        msa_transposed = self.msa_column_attention(msa_transposed, mask)
        msa_repr = msa_transposed.transpose(1, 2)  # 转置回来

        # MSA前馈网络
        msa_repr = self.msa_norm(msa_repr + self.msa_transition(msa_repr))

        # 2. MSA到Pair的更新
        pair_repr = self.outer_product_mean(msa_repr, pair_repr)

        # 3. Pair处理
        # 三角乘法
        pair_repr = self.triangle_multiplication_outgoing(pair_repr, mask)
        pair_repr = self.triangle_multiplication_incoming(pair_repr, mask)

        # 三角注意力
        pair_repr = self.triangle_attention_starting(pair_repr, mask, dim=-2)
        pair_repr = self.triangle_attention_ending(pair_repr, mask, dim=-3)

        # Pair前馈网络
        pair_repr = self.pair_norm(pair_repr + self.pair_transition(pair_repr))

        # 4. Single representation处理
        single_attended, _ = self.single_attention(single_repr, single_repr, single_repr)
        single_repr = self.single_norm(single_repr + single_attended)
        single_repr = single_repr + self.single_transition(single_repr)

        return msa_repr, pair_repr, single_repr


class EvoformerModel(nn.Module):
    """Evoformer模型主类，实现架构级融合"""

    def __init__(self, esm_dim: int = 1280, msa_dim: int = 256, pair_dim: int = 128,
                 num_blocks: int = 4, num_heads: int = 8, dropout: float = 0.1):
        """
        初始化Evoformer模型

        Args:
            esm_dim: ESM-2特征维度 (用作single representation)
            msa_dim: MSA特征维度
            pair_dim: Pair特征维度
            num_blocks: Evoformer块数量
            num_heads: 注意力头数
            dropout: Dropout率
        """
        super().__init__()

        self.esm_dim = esm_dim
        self.msa_dim = msa_dim
        self.pair_dim = pair_dim
        self.num_blocks = num_blocks

        # 输入投影层
        self.msa_input_projection = nn.Linear(21, msa_dim)  # 21 = 20 amino acids + gap
        self.single_input_projection = nn.Linear(esm_dim, esm_dim)  # 保持ESM-2维度
        self.pair_input_projection = nn.Linear(128, pair_dim)  # 从初始pair representation投影

        # Evoformer块
        self.evoformer_blocks = nn.ModuleList([
            EvoformerBlock(msa_dim, pair_dim, esm_dim, num_heads, dropout)
            for _ in range(num_blocks)
        ])

        # 输出投影层
        self.msa_output_projection = nn.Linear(msa_dim, msa_dim)
        self.pair_output_projection = nn.Linear(pair_dim, pair_dim)
        self.single_output_projection = nn.Linear(esm_dim, esm_dim)

        # 最终融合层
        self.final_fusion = nn.Sequential(
            nn.Linear(esm_dim + msa_dim, esm_dim),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(esm_dim, esm_dim)
        )

    def forward(self, single_repr: torch.Tensor, msa_repr: torch.Tensor,
                pair_repr: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        前向传播

        Args:
            single_repr: Single表示 (batch_size, seq_len, esm_dim)
            msa_repr: MSA表示 (batch_size, num_sequences, seq_len, 21)
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, 128)
            mask: 掩码

        Returns:
            输出特征字典
        """
        batch_size, seq_len, _ = single_repr.shape

        # 输入投影
        single_repr = self.single_input_projection(single_repr)
        msa_repr = self.msa_input_projection(msa_repr)
        pair_repr = self.pair_input_projection(pair_repr)

        # 存储中间结果
        msa_representations = [msa_repr]
        pair_representations = [pair_repr]
        single_representations = [single_repr]

        # 通过Evoformer块
        for i, block in enumerate(self.evoformer_blocks):
            msa_repr, pair_repr, single_repr = block(msa_repr, pair_repr, single_repr, mask)

            msa_representations.append(msa_repr)
            pair_representations.append(pair_repr)
            single_representations.append(single_repr)

        # 输出投影
        final_msa = self.msa_output_projection(msa_repr)
        final_pair = self.pair_output_projection(pair_repr)
        final_single = self.single_output_projection(single_repr)

        # 最终融合 (融合single和MSA的第一行，即查询序列)
        query_msa = final_msa[:, 0, :, :]  # (batch_size, seq_len, msa_dim)
        fused_features = self.final_fusion(torch.cat([final_single, query_msa], dim=-1))

        return {
            "fused_features": fused_features,
            "msa_representation": final_msa,
            "pair_representation": final_pair,
            "single_representation": final_single,
            "msa_evolution": msa_representations,
            "pair_evolution": pair_representations,
            "single_evolution": single_representations
        }

    def extract_and_fuse(self, sequence: str, feature_extractor, msa_database_path: str = None) -> Dict[str, torch.Tensor]:
        """
        端到端特征提取和融合

        Args:
            sequence: 蛋白质序列
            feature_extractor: 特征提取器实例
            msa_database_path: MSA数据库路径

        Returns:
            融合后的特征
        """
        self.eval()

        with torch.no_grad():
            # 提取特征
            all_features = feature_extractor.extract_features(sequence, msa_database_path)

            # 准备输入
            single_repr = all_features["single_representation"].unsqueeze(0)  # (1, seq_len, esm_dim)
            msa_repr = all_features["msa_representation"].unsqueeze(0)  # (1, num_sequences, seq_len, 21)
            pair_repr = all_features["pair_representation"].unsqueeze(0)  # (1, seq_len, seq_len, 128)

            # 移动到正确的设备
            device = next(self.parameters()).device
            single_repr = single_repr.to(device)
            msa_repr = msa_repr.to(device)
            pair_repr = pair_repr.to(device)

            # 前向传播
            results = self.forward(single_repr, msa_repr, pair_repr)

            # 添加原始特征信息
            results.update({
                "sequence": sequence,
                "sequence_length": len(sequence),
                "msa_depth": all_features.get("msa_depth", 1),
                "original_features": all_features
            })

            return results

    def get_structure_predictions(self, pair_repr: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        从pair representation预测结构信息

        Args:
            pair_repr: Pair表示 (batch_size, seq_len, seq_len, pair_dim)

        Returns:
            结构预测结果
        """
        batch_size, seq_len, _, _ = pair_repr.shape

        # 简化的距离预测 (实际应用中需要更复杂的头部网络)
        distance_logits = torch.sum(pair_repr, dim=-1)  # (batch_size, seq_len, seq_len)

        # 接触图预测
        contact_probs = torch.sigmoid(distance_logits)

        # 对称化
        contact_probs = (contact_probs + contact_probs.transpose(-1, -2)) / 2

        return {
            "contact_probabilities": contact_probs,
            "distance_logits": distance_logits,
            "pair_features": pair_repr
        }

    def analyze_representations(self, results: Dict[str, torch.Tensor]) -> Dict[str, float]:
        """
        分析不同表示的特征

        Args:
            results: 模型输出结果

        Returns:
            分析结果
        """
        analysis = {}

        # 分析MSA表示
        if "msa_representation" in results:
            msa_repr = results["msa_representation"]
            analysis["msa_mean"] = float(msa_repr.mean())
            analysis["msa_std"] = float(msa_repr.std())
            analysis["msa_sparsity"] = float((msa_repr == 0).float().mean())

        # 分析Pair表示
        if "pair_representation" in results:
            pair_repr = results["pair_representation"]
            analysis["pair_mean"] = float(pair_repr.mean())
            analysis["pair_std"] = float(pair_repr.std())
            analysis["pair_diagonal_mean"] = float(torch.diagonal(pair_repr[0], dim1=0, dim2=1).mean())

        # 分析Single表示
        if "single_representation" in results:
            single_repr = results["single_representation"]
            analysis["single_mean"] = float(single_repr.mean())
            analysis["single_std"] = float(single_repr.std())

        # 分析融合特征
        if "fused_features" in results:
            fused_features = results["fused_features"]
            analysis["fused_mean"] = float(fused_features.mean())
            analysis["fused_std"] = float(fused_features.std())

        return analysis
