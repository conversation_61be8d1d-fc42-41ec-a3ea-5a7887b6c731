"""
架构级融合模型的工具函数
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


def visualize_pair_representation(pair_repr: torch.Tensor, sequence: str,
                                 title: str = "Pair Representation", save_path: Optional[str] = None,
                                 figsize: Tuple[int, int] = (10, 8)):
    """
    可视化Pair representation
    
    Args:
        pair_repr: Pair表示 (seq_len, seq_len, pair_dim) 或 (seq_len, seq_len)
        sequence: 蛋白质序列
        title: 图像标题
        save_path: 保存路径
        figsize: 图像大小
    """
    if isinstance(pair_repr, torch.Tensor):
        pair_repr = pair_repr.cpu().numpy()
    
    # 如果是3D张量，取平均值或第一个通道
    if len(pair_repr.shape) == 3:
        pair_repr = pair_repr.mean(axis=-1)
    
    plt.figure(figsize=figsize)
    
    # 创建热图
    sns.heatmap(pair_repr, 
                cmap='viridis', 
                cbar=True,
                square=True,
                linewidths=0.1,
                cbar_kws={'label': 'Pair Feature Value'})
    
    plt.title(title)
    plt.xlabel('Residue Position')
    plt.ylabel('Residue Position')
    
    # 如果序列较短，添加氨基酸标签
    if len(sequence) <= 50:
        plt.xticks(range(len(sequence)), list(sequence), rotation=90)
        plt.yticks(range(len(sequence)), list(sequence), rotation=0)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Pair representation visualization saved to {save_path}")
    
    plt.show()


def visualize_contact_map(contact_probs: torch.Tensor, sequence: str,
                         threshold: float = 0.5, save_path: Optional[str] = None):
    """
    可视化接触图
    
    Args:
        contact_probs: 接触概率 (seq_len, seq_len)
        sequence: 蛋白质序列
        threshold: 接触阈值
        save_path: 保存路径
    """
    if isinstance(contact_probs, torch.Tensor):
        contact_probs = contact_probs.cpu().numpy()
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
    
    # 原始接触概率
    im1 = ax1.imshow(contact_probs, cmap='Blues', vmin=0, vmax=1)
    ax1.set_title('Contact Probabilities')
    ax1.set_xlabel('Residue Position')
    ax1.set_ylabel('Residue Position')
    plt.colorbar(im1, ax=ax1)
    
    # 二值化接触图
    binary_contacts = (contact_probs > threshold).astype(float)
    im2 = ax2.imshow(binary_contacts, cmap='Greys', vmin=0, vmax=1)
    ax2.set_title(f'Binary Contacts (threshold={threshold})')
    ax2.set_xlabel('Residue Position')
    ax2.set_ylabel('Residue Position')
    plt.colorbar(im2, ax=ax2)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Contact map visualization saved to {save_path}")
    
    plt.show()


def visualize_msa_representation(msa_repr: torch.Tensor, sequence: str,
                                max_sequences: int = 50, save_path: Optional[str] = None):
    """
    可视化MSA representation
    
    Args:
        msa_repr: MSA表示 (num_sequences, seq_len, msa_dim)
        sequence: 蛋白质序列
        max_sequences: 最大显示序列数
        save_path: 保存路径
    """
    if isinstance(msa_repr, torch.Tensor):
        msa_repr = msa_repr.cpu().numpy()
    
    # 限制显示的序列数量
    if msa_repr.shape[0] > max_sequences:
        msa_repr = msa_repr[:max_sequences]
    
    # 如果是3D张量，取平均值
    if len(msa_repr.shape) == 3:
        msa_repr = msa_repr.mean(axis=-1)
    
    plt.figure(figsize=(12, 8))
    
    # 创建热图
    sns.heatmap(msa_repr, 
                cmap='viridis', 
                cbar=True,
                cbar_kws={'label': 'MSA Feature Value'})
    
    plt.title('MSA Representation')
    plt.xlabel('Residue Position')
    plt.ylabel('Sequence Index')
    
    # 如果序列较短，添加氨基酸标签
    if len(sequence) <= 50:
        plt.xticks(range(len(sequence)), list(sequence), rotation=90)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"MSA representation visualization saved to {save_path}")
    
    plt.show()


def plot_representation_evolution(evolution_data: List[torch.Tensor], 
                                 representation_type: str = "msa",
                                 save_path: Optional[str] = None):
    """
    绘制表示在不同层的演化
    
    Args:
        evolution_data: 不同层的表示列表
        representation_type: 表示类型 ("msa", "pair", "single")
        save_path: 保存路径
    """
    num_layers = len(evolution_data)
    
    # 计算每层的统计信息
    means = []
    stds = []
    
    for layer_data in evolution_data:
        if isinstance(layer_data, torch.Tensor):
            layer_data = layer_data.cpu().numpy()
        
        means.append(np.mean(layer_data))
        stds.append(np.std(layer_data))
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    
    # 均值演化
    ax1.plot(range(num_layers), means, 'b-o', linewidth=2, markersize=6)
    ax1.set_xlabel('Layer')
    ax1.set_ylabel('Mean Value')
    ax1.set_title(f'{representation_type.upper()} Representation Mean Evolution')
    ax1.grid(True, alpha=0.3)
    
    # 标准差演化
    ax2.plot(range(num_layers), stds, 'r-s', linewidth=2, markersize=6)
    ax2.set_xlabel('Layer')
    ax2.set_ylabel('Standard Deviation')
    ax2.set_title(f'{representation_type.upper()} Representation Std Evolution')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Representation evolution plot saved to {save_path}")
    
    plt.show()


def analyze_evoformer_outputs(results: Dict[str, torch.Tensor]) -> Dict[str, float]:
    """
    分析Evoformer输出
    
    Args:
        results: 模型输出结果
        
    Returns:
        分析结果
    """
    analysis = {}
    
    # 分析MSA表示
    if "msa_representation" in results:
        msa_repr = results["msa_representation"]
        if isinstance(msa_repr, torch.Tensor):
            msa_repr = msa_repr.cpu().numpy()
        
        analysis.update({
            "msa_mean": float(np.mean(msa_repr)),
            "msa_std": float(np.std(msa_repr)),
            "msa_min": float(np.min(msa_repr)),
            "msa_max": float(np.max(msa_repr)),
            "msa_sparsity": float(np.mean(msa_repr == 0))
        })
    
    # 分析Pair表示
    if "pair_representation" in results:
        pair_repr = results["pair_representation"]
        if isinstance(pair_repr, torch.Tensor):
            pair_repr = pair_repr.cpu().numpy()
        
        analysis.update({
            "pair_mean": float(np.mean(pair_repr)),
            "pair_std": float(np.std(pair_repr)),
            "pair_min": float(np.min(pair_repr)),
            "pair_max": float(np.max(pair_repr)),
            "pair_diagonal_mean": float(np.mean(np.diagonal(pair_repr[0], axis1=0, axis2=1)))
        })
    
    # 分析Single表示
    if "single_representation" in results:
        single_repr = results["single_representation"]
        if isinstance(single_repr, torch.Tensor):
            single_repr = single_repr.cpu().numpy()
        
        analysis.update({
            "single_mean": float(np.mean(single_repr)),
            "single_std": float(np.std(single_repr)),
            "single_min": float(np.min(single_repr)),
            "single_max": float(np.max(single_repr))
        })
    
    # 分析融合特征
    if "fused_features" in results:
        fused_features = results["fused_features"]
        if isinstance(fused_features, torch.Tensor):
            fused_features = fused_features.cpu().numpy()
        
        analysis.update({
            "fused_mean": float(np.mean(fused_features)),
            "fused_std": float(np.std(fused_features)),
            "fused_min": float(np.min(fused_features)),
            "fused_max": float(np.max(fused_features))
        })
    
    return analysis


def save_evoformer_results(results: Dict[str, torch.Tensor], save_path: str):
    """
    保存Evoformer结果
    
    Args:
        results: 模型输出结果
        save_path: 保存路径
    """
    # 转换为numpy格式保存
    results_np = {}
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            results_np[key] = value.cpu().numpy()
        elif isinstance(value, list):
            # 处理演化数据
            if key.endswith("_evolution"):
                evolution_data = []
                for item in value:
                    if isinstance(item, torch.Tensor):
                        evolution_data.append(item.cpu().numpy())
                    else:
                        evolution_data.append(item)
                results_np[key] = evolution_data
            else:
                results_np[key] = value
        else:
            results_np[key] = value
    
    np.savez_compressed(save_path, **results_np)
    logger.info(f"Evoformer results saved to {save_path}")


def load_evoformer_results(load_path: str) -> Dict:
    """
    加载Evoformer结果
    
    Args:
        load_path: 文件路径
        
    Returns:
        结果字典
    """
    data = np.load(load_path, allow_pickle=True)
    results = {}
    
    for key in data.files:
        value = data[key]
        if isinstance(value, np.ndarray) and value.dtype != object:
            results[key] = torch.from_numpy(value)
        else:
            results[key] = value.item() if value.ndim == 0 else value
    
    logger.info(f"Evoformer results loaded from {load_path}")
    return results


def print_evoformer_summary(results: Dict[str, torch.Tensor]):
    """
    打印Evoformer分析摘要
    
    Args:
        results: 模型输出结果
    """
    print("=" * 60)
    print("EVOFORMER ARCHITECTURAL FUSION SUMMARY")
    print("=" * 60)
    
    if "sequence" in results:
        print(f"Sequence: {results['sequence'][:50]}...")
        print(f"Sequence length: {results.get('sequence_length', 'Unknown')}")
    
    if "msa_depth" in results:
        print(f"MSA depth: {results['msa_depth']}")
    
    # 分析各种表示
    analysis = analyze_evoformer_outputs(results)
    
    print("\nRepresentation Statistics:")
    for key, value in analysis.items():
        print(f"  {key}: {value:.4f}")
    
    # 分析演化
    if "msa_evolution" in results:
        print(f"\nMSA evolution layers: {len(results['msa_evolution'])}")
    
    if "pair_evolution" in results:
        print(f"Pair evolution layers: {len(results['pair_evolution'])}")
    
    if "single_evolution" in results:
        print(f"Single evolution layers: {len(results['single_evolution'])}")
    
    print("=" * 60)
